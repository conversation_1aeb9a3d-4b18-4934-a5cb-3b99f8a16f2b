page {
  width: 100%;
}

.banner {
  border-radius: 15rpx;
  /* margin: 20rpx; */
  background: white;
  padding-bottom: 0rpx;
}

.location-address {
  display: flex;
  align-items: center;
  padding: 20rpx;
  font-weight: bold;
  font-size: 34rpx;
}

.search-input-views {
  padding: 0 20rpx;
  background: white;
}

.iconsousuo-copy {
  color: #c3c3c3;
}

.search-input-view {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50rpx;
  padding: 10rpx 0;
}

.search-image-class {
  width: 44rpx;
  height: auto;
}

.search-input {
  color: #c3c3c3;
  font-size: 24rpx;
  margin-left: 10rpx;
}

.place-image {
  width: 10%;
  height: auto;
  padding-right: 20rpx;
}

.menu-swiper {
  height: 400rpx;
  position: sticky;
  top: 0;
}

.carousel_img {
  border-radius: 15rpx;
}

.index-nav-view-first {
  display: flex;
  flex-direction: column;
  margin: 30rpx 20rpx;
}

.index-nav-view-second {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.index-nav-view-third {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
}

.index-nav-view-third image {
  width: 70%;
  height: auto;
  margin-bottom: 14rpx;
  box-shadow: -2px 0px 5px 0.5px rgba(0, 0, 0, 0), 0px -2px 5px 1px rgba(0, 0, 0, 0.1), 2px 0px 5px 1px rgba(0, 0, 0, 0), 0px 2px 5px 1px rgba(0, 0, 0, 0.1);
  border-radius: 60rpx;
}

.index-nav-view-third text {
  font-size: 24rpx;
  color: rgb(0, 0, 0);
}

.carousel-swiper {
  margin: 20rpx;
  height: 258rpx;
}

.carousel-swiper-item {
  border-radius: 15rpx;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.recommend-business-title {
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.business-item {
  display: flex;
  background-color: #fff;
  padding: 20rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  align-items: center;
}

.main-image-num {
  position: relative;
}

.business-image {
  height: 160rpx;
  width: 160rpx;
}

.business-info {
  flex: 1;
  overflow: hidden;
  margin-left: 25rpx;
}
.coupons-info {
  margin-top: 5rpx;
  width: 100%;
}

.business-info-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 20rpx;
  color: #717171;
  margin-top: 5rpx;
  padding-right: 10rpx;
}


.business-fsize-color {
  font-size: 24rpx;
  color: #717171;
}

.business-name {
  font-size: 30rpx;
  line-height: 34rpx;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: black;
}
.shop-status {
  font-size: 16rpx;
  font-weight: 600;
  white-space: nowrap;
  background-color: rgb(4, 179, 4);
  color: #fff;
  padding: 5rpx 10rpx;
  border-radius: 10rpx;
}
.shop-status-offline {
  background-color: rgb(156, 156, 156);
}

.business-evaluate {
  color: #ff6500;
}

.business-right {
  margin-left: 15rpx;
}

.business-discount-list {
  padding: 1rpx 6rpx;
  font-size: 20rpx;
  margin-right: 10rpx;
}

.business-discount {
  width: 85%;
  padding-bottom: 1rpx;
  /* margin-bottom: 10rpx; */
  display: flex;
  align-items: center;
}

.settlement-view {
  position: fixed;
  z-index: 999;
  background: white;
  width: 94.8%;
  top: 0;
}

.radio-group {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  /* padding: 10rpx 10rpx 10rpx 0; */
  background: #fff;
  border-radius: 50rpx;
}
.coupons-info-scroll {
  display: flex;
  flex: 1; 
  white-space: nowrap;
  justify-content: center;
  align-items: center;
}
.group-label {
  margin-right: 5rpx;
  margin-bottom: 5rpx;
  padding: 10rpx;
  font-size: 14rpx;
  line-height: 12rpx;
  border-radius: 4rpx;
  text-align: center;
  color: #fff;
  background-color: #ff6500;
  border: solid 1px #ff6500;
}
.group-label-mall {
  color: #ff6500;
  background-color: #fff;
  border: solid 1px #ff6500;
}
.group-label-2 {
  background-color: #ff0000;
  border: solid 1px ;
}
.group-label-1 {
  background-color: #ff6500;
  border: solid 1px #ff6500;
}
.location-icon {
  width: 24rpx;
  height: 24rpx;
}
.location-text {
  font-size: 16rpx;
}

.disabled-group-label {
  background: #f5f5f5;
  color: #808080;
  border: none;
}

.radio {
  display: none;
}

/* 商品推荐 */
.like-items {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-column-gap: 10px;
  padding: 0 20rpx 10rpx 20rpx;
}

.like-item {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #f5f5f5;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  border: 1rpx #f5f5f5 solid;
}

.icon-like-class {
  width: 100%;
  height: 350rpx;
  border-radius: 15rpx 15rpx 0 0;
}

.item-two {
  margin: 0 3.5%;
}

.like-detail-view {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.fullname-class {
  margin-top: 11rpx;
  font-size: 26rpx;
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.name-text {
  width: 65%;
}

.latelyMonthlySales {
  width: 35%;
}

.num {
  position: absolute;
  top: -6px;
  right: -10px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 16px;
  font-size: 9px;
  font-weight: 700;
  color: #fff;
  background: rgb(240, 20, 20);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
}

.go-to-shop {
  padding: 0 20rpx;
  border-radius: 10rpx;
}

.engname-class {
  font-size: 24rpx;
  color: #ccc;
  width: 90%;
}

.like-money-view {
  width: 90%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0 10rpx 0;
}

.like-money {
  font-size: 26rpx;
  font-weight: bold;
}

.new-commodity-button {
  font-size: 26rpx;
}

.reduced-delivery-price {
  font-size: 30rpx;
  margin-top: 20rpx;
}

.dialog-title {
  font-size: 30rpx;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassShoppingCart {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
  padding-bottom: 12%;
}

.weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}


.weui-show .weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

/* 选择商品规格弹窗 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  z-index: 9999999;
}

/* 自定义弹窗样式 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__ft {
  padding: 20rpx 0 0 0;
  position: sticky;
  bottom: 0;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  max-height: 90vh;
  /* border-radius: 0%; */
}

.weui-show .weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

.iconweibiaoti35-copy {
  font-size: 26rpx;
}


.mall-type-swiper {
  padding: 0 20rpx;
  height: 348rpx;
}

.mall-type-swiper-item {
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  column-gap: 24rpx;
}
.mall-type-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.mall-type-icon {
  width: 90rpx;
  height: 90rpx;
  border-radius: 60rpx;
  margin-bottom: 10rpx;
}
.mall-type-icon-content {
  padding: 10rpx;
  border-radius: 80rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: #fff;
}
.mall-type-name {
  font-size: 20rpx;
  text-align: center;
}
.mall-type-swiper .mall-type-name {
  font-size: 28rpx;
  text-align: center;
  width: 120rpx;
}
.mall-category {
  display: flex;
  gap: 10rpx;
  align-items: flex-start;
  padding: 0 10rpx;
  margin-bottom: 24rpx;
}
.mall-category .mall-type-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50rpx;
}

.mall-category-scroll-content {
  display: flex;
  gap: 10rpx;
}
.icon-all {
  color: #ff6500
}
.main-content {
  background-color: #F5F5F5;
  padding-top: 20rpx;
  padding-bottom: 40rpx;
}

.select-classfiy {
  display: flex;
  background-color: #fff;
  border-radius: 20rpx;
  margin: 10rpx 20rpx 20rpx;
  padding: 20rpx 20rpx;
  font-size: 30rpx;
}
.select-classfiy-item {
  color: #717171;
  margin: 0 20rpx;
}
.select-classfiy-item {
  color: #717171;
  margin: 0 20rpx;
}