# 支付页面价格计算重构说明

## 概述

本次重构主要解决了支付页面中金额计算逻辑分散、数据状态不一致、页面间数据传递混乱等问题。通过引入统一的价格计算器和页面数据管理器，确保了价格计算的准确性和一致性。

## 重构内容

### 1. 新增工具类

#### PriceCalculator (价格计算器)
- **位置**: `utils/price-calculator.js`
- **功能**: 统一管理所有价格计算逻辑
- **特性**:
  - 集中式价格计算
  - 数据验证机制
  - 精度控制
  - 调试信息输出

#### PageDataManager (页面数据管理器)
- **位置**: `utils/page-data-manager.js`
- **功能**: 管理页面间数据传递
- **特性**:
  - 安全的页面数据更新
  - 批量数据更新
  - 页面状态验证

#### PriceDebugHelper (价格调试助手)
- **位置**: `utils/price-debug-helper.js`
- **功能**: 提供价格计算调试功能
- **特性**:
  - 详细计算过程展示
  - 问题诊断
  - 性能监控

### 2. 重构的页面

#### 支付页面 (pages/menu/pay/pay.js)
- 集成价格计算器
- 统一金额计算逻辑
- 添加调试功能
- 优化配送费计算

#### 优惠券页面 (pages/mine/coupons/coupons.js)
- 使用页面数据管理器
- 优化数据传递逻辑

#### 地址选择页面 (pages/address/choose/choose.js)
- 使用页面数据管理器
- 简化配送费更新逻辑

## 使用方法

### 基本使用

```javascript
// 在支付页面中
import PriceCalculator from '../../../utils/price-calculator';

// 初始化价格计算器
this.priceCalculator = new PriceCalculator();

// 设置基础数据
this.priceCalculator
  .setBasePrice(100)
  .setPackingCharges(5)
  .setDeliveryMode(true)
  .setDeliveryFee(8);

// 获取最终价格
const finalPrice = this.priceCalculator.calculateFinalPrice();

// 更新页面显示
this.updatePriceDisplay();
```

### 调试功能

```javascript
// 开启调试模式
this.enableDebugMode();

// 快速检查价格状态
const status = this.quickPriceCheck();

// 诊断价格计算问题
this.diagnosePriceIssues();

// 关闭调试模式
this.disableDebugMode();
```

### 页面间数据传递

```javascript
import PageDataManager from '../../../utils/page-data-manager';

// 更新支付页面价格
PageDataManager.updatePayPagePrice({
  couponInfo: {
    id: 1,
    price: 15,
    couponsName: '优惠券'
  }
});

// 批量更新
PageDataManager.batchUpdatePayPage({
  price: { deliveryFee: 10 },
  delivery: { currentTab: 0 },
  other: { time: '12:00' }
});
```

## 价格计算流程

### 计算顺序
1. 商品基础价格
2. 加上包装费
3. 减去优惠券折扣
4. 减去满减折扣
5. 加上配送费
6. 加上上楼费
7. 得到最终价格

### 计算公式
```
最终价格 = 商品基础价格 + 包装费 - 优惠券折扣 - 满减折扣 + 配送费 + 上楼费
```

### 特殊规则
- 自取模式下配送费和上楼费为0
- 优惠券折扣不能超过商品总价
- 满减需要满足最低消费条件
- 最终价格不能为负数

## 测试验证

### 运行测试
```bash
# 在项目根目录下运行
node test-price-refactor.js
```

### 测试覆盖
- ✅ 基础价格计算
- ✅ 配送费计算
- ✅ 优惠券计算
- ✅ 满减计算
- ✅ 复杂场景组合
- ✅ 边界情况处理
- ✅ 性能测试
- ✅ 数据验证

## 常见问题

### Q: 价格计算不准确怎么办？
A: 
1. 开启调试模式查看详细计算过程
2. 使用诊断功能检查问题
3. 检查数据验证结果

### Q: 页面间数据传递失败？
A: 
1. 确保目标页面存在且已初始化
2. 检查传递的数据格式是否正确
3. 查看控制台错误信息

### Q: 如何添加新的优惠类型？
A: 
1. 在PriceCalculator中添加新的计算方法
2. 更新价格计算流程
3. 添加相应的测试用例

## 性能优化

### 计算性能
- 单次计算耗时 < 1ms
- 10000次计算 < 1秒
- 内存占用最小化

### 调试模式
- 生产环境建议关闭调试模式
- 调试信息仅在开发环境输出
- 可通过配置控制调试级别

## 兼容性说明

### 向后兼容
- 保留原有的data结构
- 兼容原有的页面调用方式
- 渐进式迁移支持

### 数据格式
- 所有金额保持2位小数精度
- 使用utilHelper.toFixed确保精度
- 支持原有的数据字段

## 维护建议

### 代码维护
1. 新增功能优先使用价格计算器
2. 定期运行测试确保功能正常
3. 及时更新文档和注释

### 问题排查
1. 优先使用调试工具定位问题
2. 检查数据验证结果
3. 对比计算过程找出差异

### 性能监控
1. 定期检查计算性能
2. 监控内存使用情况
3. 优化频繁调用的方法

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 新增统一价格计算器
- ✨ 新增页面数据管理器
- ✨ 新增调试工具
- 🔧 重构支付页面计算逻辑
- 🔧 重构优惠券选择逻辑
- 🔧 重构地址选择逻辑
- ✅ 添加完整测试覆盖
- 📚 添加详细文档说明

## 联系方式

如有问题或建议，请联系开发团队。
