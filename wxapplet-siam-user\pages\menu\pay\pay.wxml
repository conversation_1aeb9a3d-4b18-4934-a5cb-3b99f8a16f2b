<view class="top-detail" wx:if="{{tipReward}}">
	<view class="current-tips" catchtap="customerServiceWechat">
		{{tipReward}}
	</view>
</view>
<view class="ask-for-delivery-detail">
	<view class="swiper-tab self-adaption">
		<view class="swiper-tab-item" wx:for="{{modeTabList}}" wx:key="index" data-current="{{index}}" bindtap="clickTab" hover-class='hover-click-class'>
			<view class="swiper_table_item_view {{deliveryAndSelfTaking.currentTab==item.modeId?'active':''}}" data-current="{{index}}" bindtap="clickTab">
				{{item.modeName}}
			</view> 
		</view>
	</view>
	<view bindchange="bindSlideChange" class="bindSlideChange">
		<view wx:if="{{deliveryAndSelfTaking.currentTab==0}}" class="bindSlideChange-class">
			<view class="space-between-class">
				<view class="deliveryAddress-info">
					<view class="ask-for-delivery-house-view" wx:if="{{deliveryAndSelfTaking.deliveryAddress}}" data-radioIndex="0" bindtap="radioChange">
						<view class="ask-for-delivery-view">
							<view class="ask-for-delivery-house">
								<text class="iconfont icondizhi"></text>
								<view class="right-class out_of_range two_row">
                {{deliveryAndSelfTaking.deliveryAddress.province?deliveryAndSelfTaking.deliveryAddress.province:''}}{{deliveryAndSelfTaking.deliveryAddress.city?deliveryAndSelfTaking.deliveryAddress.city:''}}
								{{deliveryAndSelfTaking.deliveryAddress.area?deliveryAndSelfTaking.deliveryAddress.area:''}}{{deliveryAndSelfTaking.deliveryAddress.street}}</view>
							</view>
							<view class="ask-for-delivery-address-detail">
								<text class="iconfont icon002dianhua"></text>
								<view class="right-class">
									<text>{{deliveryAndSelfTaking.deliveryAddress.phone}}</text>
									<text decode="true">&nbsp;&nbsp;&nbsp;{{deliveryAndSelfTaking.deliveryAddress.realname}}{{deliveryAndSelfTaking.deliveryAddress.sex==0?"先生":"女士"}}</text>
								</view>
							</view>
						</view>
						<text class="iconfont iconhtbArrowright02"></text>
					</view>
					<view class="view-line" wx:if="{{deliveryAndSelfTaking.deliveryAddress}}"></view>
					<view class="dayue-time" wx:if="{{deliveryAndSelfTaking.deliveryAddress}}">
						<text>立即送出</text>
						<view class="dayue-time-songda-view">
							<text class="dayue-time-songda theme-color">大约{{time}}送达</text>
						</view>
					</view>
					<view class="ask-for-delivery-address-detail" wx:else data-radioIndex="0" bindtap="radioChange">
						<text>去选择地址</text>
						<text class="iconfont iconhtbArrowright02"></text>
					</view>
				</view>
			</view>
		</view>
		<view wx:if="{{deliveryAndSelfTaking.currentTab==1}}">
			<view class="currentTab1-view">
				<view class="currentTab1-title">商家地址</view>
				<view>{{shopAddress.shop.name}}</view>
				<view>{{shopAddress.shop.province?shopAddress.shop.province:''}}{{shopAddress.shop.city?shopAddress.shop.city:''}}{{shopAddress.shop.area?shopAddress.shop.area:''}}{{shopAddress.shop.street}}</view>
			</view>
			<view class="time-phone-view">
				<view class="time-phone-item">
					<view class="time-phone-title">自取时间</view>
					<view class="time-phone-content">
						<view>{{time}}</view>
					</view>
				</view>
				<view class="time-phone-item">
					<view class="time-phone-title">预留电话</view>
					<view class="time-phone-content">
						<input value="{{phoneNumber}}"></input>
						<text class="iconfont iconhtbArrowright02"></text>
					</view>
				</view>
			</view>
		</view>
	</view>
</view>
<view class="shopping-commodity-details">
	<view>
		{{shopAddress.shop.name}}
	</view>
	<view class="commodity-name-price-detail" wx:for="{{data.orderDetailList}}" wx:key="index">
		<view class="commodity-name-types">
			<view class="commodity-name">{{item.goodsName}}</view>
			<view class="commodity-types">{{item.restructure}}</view>
		</view>
		<view class="commodity-totalnum-price">
			<view class="commodity-totalnum">x{{item.number}}</view>
			<view class="commodity-price">￥{{item.totalPrice}}</view>
		</view>
	</view>
	<view class="commodity-name-price-detail">
		<view class="commodity-name-types">
			<view class="commodity-name">包装费</view>
			<view class="commodity-types commodity-delivery-tip theme-color">商品包装费</view>
		</view>
		<view class="commodity-totalnum-price">
			<view></view>
			<view class="commodity-price">￥{{data.packingCharges}}</view>
		</view>
	</view>
	<view class="commodity-name-price-detail" wx:if="{{deliveryAndSelfTaking.currentTab==0}}">
		<view class="commodity-name-types">
			<view class="commodity-name">配送费</view>
		</view>
		<view class="commodity-totalnum-price">
			<view></view>
			<view class="commodity-price">
				<text wx:if="{{deliveryAndSelfTaking.isReducedDeliveryPrice}}" class="fullPriceReductionClass">￥{{deliveryAndSelfTaking.reducedDeliveryTotalPrice}}</text>
				<text decode="true">&nbsp;&nbsp;&nbsp;￥{{deliveryAndSelfTaking.feeData}}</text>
			</view>
		</view>
	</view>
	<view class="commodity-name-price-detail" wx:if="{{data.fullPriceReductionIsHidden}}">
		<view class="commodity-name-types">
			<view class="commodity-name">满减</view>
		</view>
		<view class="commodity-totalnum-price">
			<view></view>
			<view class="commodity-price full-reduction-view theme-color-border">{{data.fullReductionRuleName}}</view>
		</view>
	</view>
	<view class="view-line"></view>
	<view class="total-money-view">
		<text class="money-icon commodity-price">总计：</text>
		<view class="{{data.fullPriceReductionIsHidden||data.couponsIsHidden||deliveryAndSelfTaking.isThereADiscount?'fullPriceReductionClass':''}}">
			<text class="commodity-price">￥{{data.actualPrice}}</text>
		</view>
		<text class="commodity-price" wx:if="{{data.fullPriceReductionIsHidden||data.couponsIsHidden||deliveryAndSelfTaking.isThereADiscount}}" decode="true">&nbsp;&nbsp;&nbsp;￥{{data.fullPriceReduction}}
		</text>
	</view>
</view>
<view class="pay-mode-view">
	<view class="pay-mode-title">优惠券</view>
	<view class="choose-pay-mode coupon-mode" bindtap="{{afterDiscount?'onCoupon':''}}">
		<view class="theme-color after-discount flex_end" wx:if="{{afterDiscount}}">
			<view class="out_of_range tow_row">
				<text>{{afterDiscount.couponsName?afterDiscount.couponsName:"未使用优惠券 "}}</text>
				<text decode="true" class="">&nbsp;&nbsp;已优惠{{afterDiscount.price}}元</text>
			</view>
		</view> 
		<view class="theme-color after-discount" wx:else>
			<text>无可用优惠券</text>
		</view>
		<text class="iconfont iconhtbArrowright02"></text>
	</view>
</view>
<view class="pay-mode-view" wx:if="{{shopAddress.shop.deliveryWay != 1}}">
	<view class="pay-mode-title"><text>是否上楼</text></view>
	<view class="delivery-upstairs">
		<view class="theme-color checkbox {{isGoUpstairs ? 'checked' : ''}}" bindtap="upstairsTap" data-isGoUpstairs="{{true}}">
      <text>是</text>
		</view> 
		<view class="theme-color checkbox {{!isGoUpstairs ? 'checked' : ''}}" bindtap="upstairsTap" data-isGoUpstairs="{{false}}">
			<text>否</text>
		</view>
	</view>
</view>
<!-- <view class="pay-mode-view">
	<view>支付方式</view>
	<view class="choose-pay-mode" bindtap="choosePayModeTap">
		<text class="iconfont {{paymentModes[paymentModeIndex].icon}}"></text>
		<text decode="true">&nbsp;{{paymentModes[paymentModeIndex].text}}</text>
		<text class="iconfont iconhtbArrowright02"></text>
	</view>
</view> -->
<view class="pay-mode-view margin-border-radius">
  <view>支付方式</view>
  <view class="choose-pay-mode">
    <radio-group class="radio-group-address" bindchange="radioChangeAddress" data-firstIndex="{{key}}">
      <label wx:for="{{paymentModes}}" wx:key="index" wx:if="{{item.show}}"
      class="radio-label-payment flex_center {{item.checked?'payment-checked':'payment-not-checked'}}">
        <radio value="{{index}}" checked="{{item.checked}}" class="pay_radio" />
        <text class="iconfont {{item.icon}}"></text>
        <text decode="true">&nbsp;{{item.text}}</text>
        <text decode="true" class="actionItem__desc" wx:if="{{item.desc}}">&nbsp;{{item.desc}}</text>
      </label>
    </radio-group>
  </view>
</view>
<view class="ask-for-remarks-view">
	<!-- <view class="choose-ask-for" wx:if="{{radioIndex==0}}">
    <view>自取方式</view>
    <radio-group class="ask-for-radio-group">
      <label wx:for="{{modeList[radioIndex].mode}}" wx:key="index" class="ask-for-label">
        <radio value="{{index}}" checked="{{item.checked}}" class="ask-for-radio"/>{{item.name}}
      </label>
    </radio-group>
  </view> -->
	<view class="view-line" wx:if="{{radioIndex==0}}"></view>
	<view class="remarks-view">
		<view class="remarks-title-input-num">
			<view class="remarks-title">特殊备注要求</view>
			<view class="remarks-input-num">{{inputLength}}/30</view>
		</view>
		<textarea class="textarea-remarks" bindinput="remarksInput" maxlength="30" 
		placeholder="输入其他特殊备注要求（可不填）" placeholder-class="textarea-placeholder"></textarea>
	</view>
</view>
<view class="go-pay-view">
	<view class="go-pay-money">
		<view class="more-pay">
			<view>还需支付</view>
		</view>
		<view class="total-money" wx:if="{{!data.fullPriceReductionIsHidden&&!data.couponsIsHidden&&!deliveryAndSelfTaking.isThereADiscount}}">￥{{data.actualPrice}}</view>
		<view class="total-money" wx:else>￥{{data.fullPriceReduction}}</view>
	</view>
	<view class="go-pay theme-bg" hover-class="hover-class-public" 
	bindtap="{{isForgetThePassword?'showPwdLayer':'getRequestSubscribeMessage'}}">
		去支付
	</view>
</view>
<mp-actionSheet bindactiontap="goToPay" show="{{dialogShow}}" actions="{{paymentModes}}" title="{{title}}">
</mp-actionSheet>
<mp-halfScreenDialog show="{{showPayPwdInput}}" extClass="extClassPayPwd" bind:bindtouchend="bindtouchend"
bind:close="balancePayFail" maskClosable="{{maskClosable}}">
	<view slot="title">输入支付密码</view>
	<view slot="desc">
		<view class='password_dialog_tip'><text>使用会员卡余额支付需要验证身份，验证通过后才可进行支付。</text></view>
		<view class='password_dialog_row' catchtap='getFocus'>
			<view class='password_dialog_item_input' wx:for='{{6}}' wx:key='item' wx:for-index='i'>
				<text wx:if='{{pwdVal.length>i}}'></text>
			</view>
		</view>
		<view class='theme-color password_dialog_forget_pwd' catchtap='forgetThePassword'>忘记密码</view>
		<input class='password_dialog_input_control' password type='number' focus='{{payFocus}}' hold-keyboard="{{holdKeyboard}}" value="{{pwdVal}}" bindinput='inputPwd'
			maxlength='6' adjust-position="{{adjustPosition}}" cursor-spacing="100"/>
	</view>
	<view slot="footer">
	</view>
</mp-halfScreenDialog>
<mp-dialog show="{{isVipDialogShow}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}" catchtouchmove="true" extClass="extClassIsVip"
ext-class="{{extClass}}" bind:tap="close">
	<image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/vip_recharge_guide.png?v={{timestamp}}" mode="widthFix" class="now-order-image" bindtap="goToRecharge"></image>
</mp-dialog>
