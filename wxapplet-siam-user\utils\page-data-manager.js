/**
 * 页面数据管理器
 * 负责页面间数据传递和状态同步，避免直接操作prevPage
 */
class PageDataManager {
  
  /**
   * 获取指定路由的页面实例
   */
  static getPageByRoute(routeName) {
    const pages = getCurrentPages();
    return pages.find(page => page.route && page.route.includes(routeName));
  }

  /**
   * 获取支付页面实例
   */
  static getPayPage() {
    return this.getPageByRoute('pay/pay');
  }

  /**
   * 获取上一个页面实例
   */
  static getPrevPage() {
    const pages = getCurrentPages();
    return pages.length > 1 ? pages[pages.length - 2] : null;
  }

  /**
   * 更新支付页面的价格信息
   */
  static updatePayPagePrice(priceData) {
    const payPage = this.getPayPage();
    if (!payPage || !payPage.priceCalculator) {
      console.warn('支付页面或价格计算器不存在');
      return false;
    }

    try {
      // 更新价格计算器
      if (priceData.basePrice !== undefined) {
        payPage.priceCalculator.setBasePrice(priceData.basePrice);
      }
      
      if (priceData.packingCharges !== undefined) {
        payPage.priceCalculator.setPackingCharges(priceData.packingCharges);
      }
      
      if (priceData.deliveryFee !== undefined) {
        payPage.priceCalculator.setDeliveryFee(priceData.deliveryFee);
      }
      
      if (priceData.isDelivery !== undefined) {
        payPage.priceCalculator.setDeliveryMode(priceData.isDelivery);
      }
      
      if (priceData.isUpstairs !== undefined || priceData.upstairsFee !== undefined) {
        payPage.priceCalculator.setUpstairsOption(
          priceData.isUpstairs !== undefined ? priceData.isUpstairs : payPage.priceCalculator.isUpstairs,
          priceData.upstairsFee !== undefined ? priceData.upstairsFee : payPage.priceCalculator.upstairsFee
        );
      }
      
      if (priceData.couponInfo !== undefined) {
        payPage.priceCalculator.setCoupon(priceData.couponInfo);
      }
      
      if (priceData.fullReductionInfo !== undefined) {
        payPage.priceCalculator.setFullReduction(priceData.fullReductionInfo);
      }

      // 触发页面更新
      payPage.updatePriceDisplay();
      return true;
    } catch (error) {
      console.error('更新支付页面价格失败:', error);
      return false;
    }
  }

  /**
   * 更新支付页面的配送信息
   */
  static updatePayPageDelivery(deliveryData) {
    const payPage = this.getPayPage();
    if (!payPage) {
      console.warn('支付页面不存在');
      return false;
    }

    try {
      // 更新配送相关数据
      const updateData = {};
      
      if (deliveryData.deliveryAddress !== undefined) {
        updateData['deliveryAndSelfTaking.deliveryAddress'] = deliveryData.deliveryAddress;
      }
      
      if (deliveryData.currentTab !== undefined) {
        updateData['deliveryAndSelfTaking.currentTab'] = deliveryData.currentTab;
      }
      
      if (deliveryData.feeData !== undefined) {
        updateData['deliveryAndSelfTaking.feeData'] = deliveryData.feeData;
      }
      
      if (deliveryData.shopAddress !== undefined) {
        updateData.shopAddress = deliveryData.shopAddress;
      }

      if (Object.keys(updateData).length > 0) {
        payPage.setData(updateData);
      }

      return true;
    } catch (error) {
      console.error('更新支付页面配送信息失败:', error);
      return false;
    }
  }

  /**
   * 更新支付页面的优惠券信息
   */
  static updatePayPageCoupon(couponData) {
    const payPage = this.getPayPage();
    if (!payPage) {
      console.warn('支付页面不存在');
      return false;
    }

    try {
      // 更新优惠券信息
      const updateData = {};
      
      if (couponData.afterDiscount !== undefined) {
        updateData.afterDiscount = couponData.afterDiscount;
      }

      if (Object.keys(updateData).length > 0) {
        payPage.setData(updateData);
      }

      // 同时更新价格计算器中的优惠券信息
      if (couponData.couponInfo !== undefined) {
        this.updatePayPagePrice({ couponInfo: couponData.couponInfo });
      }

      return true;
    } catch (error) {
      console.error('更新支付页面优惠券信息失败:', error);
      return false;
    }
  }

  /**
   * 安全的页面数据更新
   */
  static safeUpdatePageData(page, data) {
    if (!page || !page.setData) {
      console.warn('页面实例无效');
      return false;
    }

    try {
      page.setData(data);
      return true;
    } catch (error) {
      console.error('页面数据更新失败:', error);
      return false;
    }
  }

  /**
   * 获取页面数据
   */
  static getPageData(page, key) {
    if (!page || !page.data) {
      return null;
    }

    if (key) {
      return this.getNestedValue(page.data, key);
    }

    return page.data;
  }

  /**
   * 获取嵌套对象的值
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 设置嵌套对象的值
   */
  static setNestedValue(obj, path, value) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    const target = keys.reduce((current, key) => {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
    return obj;
  }

  /**
   * 验证页面状态
   */
  static validatePageState(page, requiredFields = []) {
    if (!page || !page.data) {
      return { isValid: false, error: '页面实例无效' };
    }

    const missingFields = requiredFields.filter(field => {
      const value = this.getNestedValue(page.data, field);
      return value === null || value === undefined;
    });

    if (missingFields.length > 0) {
      return { 
        isValid: false, 
        error: `缺少必要字段: ${missingFields.join(', ')}` 
      };
    }

    return { isValid: true };
  }

  /**
   * 调试信息
   */
  static getDebugInfo() {
    const pages = getCurrentPages();
    const payPage = this.getPayPage();
    
    return {
      totalPages: pages.length,
      currentPage: pages[pages.length - 1]?.route || 'unknown',
      payPageExists: !!payPage,
      payPageRoute: payPage?.route || 'not found',
      payPageHasCalculator: !!(payPage && payPage.priceCalculator),
      pageRoutes: pages.map(page => page.route)
    };
  }

  /**
   * 批量更新支付页面数据
   */
  static batchUpdatePayPage(updates) {
    const payPage = this.getPayPage();
    if (!payPage) {
      console.warn('支付页面不存在');
      return false;
    }

    try {
      // 更新价格相关数据
      if (updates.price) {
        this.updatePayPagePrice(updates.price);
      }

      // 更新配送相关数据
      if (updates.delivery) {
        this.updatePayPageDelivery(updates.delivery);
      }

      // 更新优惠券相关数据
      if (updates.coupon) {
        this.updatePayPageCoupon(updates.coupon);
      }

      // 更新其他数据
      if (updates.other) {
        this.safeUpdatePageData(payPage, updates.other);
      }

      return true;
    } catch (error) {
      console.error('批量更新支付页面失败:', error);
      return false;
    }
  }
}

export default PageDataManager;
