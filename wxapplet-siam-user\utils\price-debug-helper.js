/**
 * 价格计算调试助手
 * 提供详细的价格计算过程展示和问题诊断功能
 */
class PriceDebugHelper {
  
  /**
   * 显示价格计算的详细过程
   */
  static showCalculationProcess(priceCalculator) {
    if (!priceCalculator) {
      console.log('❌ 价格计算器不存在');
      return;
    }
    
    const debugInfo = priceCalculator.getDebugInfo();
    
    console.log('\n🔍 === 价格计算详细过程 ===');
    console.log('📊 基础数据:');
    console.log(`  商品基础价格: ¥${debugInfo.basePrice}`);
    console.log(`  包装费: ¥${debugInfo.packingCharges}`);
    console.log(`  配送模式: ${debugInfo.isDelivery ? '配送' : '自取'}`);
    console.log(`  上楼选项: ${debugInfo.isUpstairs ? '是' : '否'}`);
    
    console.log('\n💰 费用明细:');
    console.log(`  配送费: ¥${debugInfo.deliveryFee}`);
    console.log(`  上楼费: ¥${debugInfo.upstairsFee}`);
    console.log(`  优惠券折扣: -¥${debugInfo.couponDiscount}`);
    console.log(`  满减折扣: -¥${debugInfo.fullReductionDiscount}`);
    
    console.log('\n🧮 计算步骤:');
    console.log(`  步骤1 - 商品基础价格: ¥${debugInfo.calculation.step1_base}`);
    console.log(`  步骤2 - 加上包装费: ¥${debugInfo.calculation.step2_withPacking}`);
    console.log(`  步骤3 - 减去优惠券: ¥${debugInfo.calculation.step3_withCoupon}`);
    console.log(`  步骤4 - 减去满减: ¥${debugInfo.calculation.step4_withFullReduction}`);
    console.log(`  步骤5 - 加上配送费: ¥${debugInfo.calculation.step5_withDelivery}`);
    console.log(`  步骤6 - 最终价格: ¥${debugInfo.calculation.step6_final}`);
    
    if (debugInfo.couponInfo) {
      console.log('\n🎫 优惠券信息:');
      console.log(`  优惠券ID: ${debugInfo.couponInfo.id}`);
      console.log(`  优惠券名称: ${debugInfo.couponInfo.couponsName}`);
      console.log(`  折扣金额: ¥${debugInfo.couponDiscount}`);
    }
    
    if (debugInfo.fullReductionInfo) {
      console.log('\n🎁 满减信息:');
      console.log(`  满减规则: ${debugInfo.fullReductionInfo.name}`);
      console.log(`  满减条件: 满¥${debugInfo.fullReductionInfo.limitedPrice}`);
      console.log(`  减免金额: ¥${debugInfo.fullReductionInfo.reducedPrice}`);
    }
    
    console.log('\n✅ 最终结果:');
    console.log(`  应付金额: ¥${debugInfo.finalPrice}`);
    console.log(`  总优惠: ¥${debugInfo.totalDiscount}`);
    
    if (!debugInfo.validation.isValid) {
      console.log('\n⚠️ 数据验证警告:');
      debugInfo.validation.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }
    
    console.log('=== 价格计算过程结束 ===\n');
  }
  
  /**
   * 比较两个价格计算器的结果
   */
  static compareCalculators(calculator1, calculator2, label1 = '计算器1', label2 = '计算器2') {
    console.log(`\n🔄 === ${label1} vs ${label2} 对比 ===`);
    
    const details1 = calculator1.getPriceDetails();
    const details2 = calculator2.getPriceDetails();
    
    const fields = [
      { key: 'basePrice', name: '基础价格' },
      { key: 'packingCharges', name: '包装费' },
      { key: 'deliveryFee', name: '配送费' },
      { key: 'upstairsFee', name: '上楼费' },
      { key: 'couponDiscount', name: '优惠券折扣' },
      { key: 'fullReductionDiscount', name: '满减折扣' },
      { key: 'finalPrice', name: '最终价格' }
    ];
    
    fields.forEach(field => {
      const value1 = details1[field.key];
      const value2 = details2[field.key];
      const diff = Math.abs(value1 - value2);
      const status = diff < 0.01 ? '✅' : '❌';
      
      console.log(`${status} ${field.name}: ${label1}=¥${value1}, ${label2}=¥${value2}, 差异=¥${diff.toFixed(2)}`);
    });
    
    console.log('=== 对比结束 ===\n');
  }
  
  /**
   * 诊断价格计算问题
   */
  static diagnoseIssues(priceCalculator, expectedPrice = null) {
    console.log('\n🔧 === 价格计算问题诊断 ===');
    
    const debugInfo = priceCalculator.getDebugInfo();
    const issues = [];
    
    // 检查基础数据
    if (debugInfo.basePrice <= 0) {
      issues.push('基础价格为0或负数');
    }
    
    // 检查配送逻辑
    if (!debugInfo.isDelivery && (debugInfo.deliveryFee > 0 || debugInfo.upstairsFee > 0)) {
      issues.push('自取模式下仍有配送费或上楼费');
    }
    
    if (debugInfo.isDelivery && !debugInfo.isUpstairs && debugInfo.upstairsFee > 0) {
      issues.push('未选择上楼但有上楼费');
    }
    
    // 检查优惠券逻辑
    if (debugInfo.couponDiscount > debugInfo.basePrice + debugInfo.packingCharges) {
      issues.push('优惠券折扣超过了商品总价');
    }
    
    // 检查满减逻辑
    if (debugInfo.fullReductionInfo) {
      const baseForFullReduction = debugInfo.basePrice + debugInfo.packingCharges - debugInfo.couponDiscount;
      if (baseForFullReduction < debugInfo.fullReductionInfo.limitedPrice) {
        issues.push('不满足满减条件但仍应用了满减');
      }
    }
    
    // 检查最终价格
    if (debugInfo.finalPrice < 0) {
      issues.push('最终价格为负数');
    }
    
    // 与期望价格对比
    if (expectedPrice !== null) {
      const diff = Math.abs(debugInfo.finalPrice - expectedPrice);
      if (diff > 0.01) {
        issues.push(`最终价格与期望不符，期望¥${expectedPrice}，实际¥${debugInfo.finalPrice}，差异¥${diff.toFixed(2)}`);
      }
    }
    
    // 输出诊断结果
    if (issues.length === 0) {
      console.log('✅ 未发现问题');
    } else {
      console.log('❌ 发现以下问题:');
      issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    // 输出修复建议
    if (issues.length > 0) {
      console.log('\n💡 修复建议:');
      issues.forEach(issue => {
        if (issue.includes('基础价格')) {
          console.log('  - 检查商品价格计算逻辑');
        }
        if (issue.includes('配送费') || issue.includes('上楼费')) {
          console.log('  - 检查配送模式切换逻辑');
        }
        if (issue.includes('优惠券')) {
          console.log('  - 检查优惠券应用条件和金额计算');
        }
        if (issue.includes('满减')) {
          console.log('  - 检查满减规则的判断条件');
        }
        if (issue.includes('负数')) {
          console.log('  - 添加价格保护逻辑，确保最终价格不为负');
        }
      });
    }
    
    console.log('=== 诊断结束 ===\n');
    
    return issues;
  }
  
  /**
   * 生成价格计算报告
   */
  static generateReport(priceCalculator, orderInfo = {}) {
    const debugInfo = priceCalculator.getDebugInfo();
    const timestamp = new Date().toLocaleString();
    
    const report = {
      timestamp: timestamp,
      orderInfo: orderInfo,
      priceDetails: debugInfo,
      summary: {
        baseAmount: debugInfo.basePrice + debugInfo.packingCharges,
        totalDiscount: debugInfo.couponDiscount + debugInfo.fullReductionDiscount,
        totalFees: debugInfo.deliveryFee + debugInfo.upstairsFee,
        finalAmount: debugInfo.finalPrice,
        isValid: debugInfo.validation.isValid
      }
    };
    
    console.log('\n📋 === 价格计算报告 ===');
    console.log(`生成时间: ${timestamp}`);
    console.log(`订单信息: ${JSON.stringify(orderInfo, null, 2)}`);
    console.log(`价格详情: ${JSON.stringify(report.summary, null, 2)}`);
    console.log('=== 报告结束 ===\n');
    
    return report;
  }
  
  /**
   * 监控价格变化
   */
  static monitorPriceChanges(priceCalculator, actionName) {
    const beforeState = priceCalculator.clone();
    
    return {
      finish: () => {
        const afterState = priceCalculator;
        console.log(`\n📈 === 价格变化监控: ${actionName} ===`);
        
        const beforePrice = beforeState.calculateFinalPrice();
        const afterPrice = afterState.calculateFinalPrice();
        const change = afterPrice - beforePrice;
        
        console.log(`操作前价格: ¥${beforePrice}`);
        console.log(`操作后价格: ¥${afterPrice}`);
        console.log(`价格变化: ${change >= 0 ? '+' : ''}¥${change.toFixed(2)}`);
        
        if (Math.abs(change) > 0.01) {
          this.compareCalculators(beforeState, afterState, '操作前', '操作后');
        }
        
        console.log('=== 监控结束 ===\n');
      }
    };
  }
  
  /**
   * 快速检查价格计算器状态
   */
  static quickCheck(priceCalculator) {
    if (!priceCalculator) {
      return '❌ 价格计算器不存在';
    }
    
    const validation = priceCalculator.validate();
    const finalPrice = priceCalculator.calculateFinalPrice();
    
    if (!validation.isValid) {
      return `❌ 数据验证失败: ${validation.errors.join(', ')}`;
    }
    
    if (finalPrice < 0) {
      return `⚠️ 最终价格为负数: ¥${finalPrice}`;
    }
    
    return `✅ 状态正常，最终价格: ¥${finalPrice}`;
  }
}

export default PriceDebugHelper;
