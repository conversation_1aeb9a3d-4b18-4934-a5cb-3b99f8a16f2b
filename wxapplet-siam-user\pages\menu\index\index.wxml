<!-- <view wx:if="{{!netWorkType}}">
	<image src="https://shop-beingwell.oss-cn-hangzhou.aliyuncs.com/data/images/business/background.jpg" class="top-bg-class" mode="widthFix"></image>
</view> -->
<view class="content-class" wx:if="{{!netWorkType}}">
  <view id="page-top-view" class="public-views">
    <image src="{{shopInfo.shop.firstPoster}}" class="slide-image" mode='aspectFill' />
    <view class="business-view">
      <image src="{{shopInfo.shop.shopLogoImg}}" class="business-image" mode="aspectFill"></image>
    </view>
    <view class="business-info">
      <view class="business-info-name" bindtap="isShopDetailTap">{{shopInfo.shop.name}}</view>
      <view class="business-info-other">
        <text decode="true">评价&nbsp;&nbsp;{{shopInfo.shop.serviceRating}}</text>
        <!-- <text decode="true">&nbsp;&nbsp;&nbsp;月售874单</text>
				<text decode="true">&nbsp;&nbsp;&nbsp;蜂鸟转送约40分钟</text> -->
      </view>
      <view bindtap="isPromotionTap">
        <view class="business-discount-info business-info-flex" wx:if="{{shopInfo.promotionList.length>0}}">
          <view class="business-discount out_of_range one_row">
            <view class="theme-color-border business-discount-list" wx:for="{{shopInfo.promotionList}}" wx:key="index" wx:if="{{index<4}}" wx:for-item="rule">
              {{rule.name}}
            </view>
          </view>
          <view class="other-promotionList">
            <text wx:if="{{shopInfo.promotionList.length>4}}" decode="true">等{{shopInfo.promotionList.length}}个优惠&nbsp;</text>
            <text class="iconfont iconweibiaoti35-copy"></text>
          </view>
        </view>
        <view wx:elif="{{shopInfo.shop.reducedDeliveryPrice}}" class="business-discount-list theme-color">
          配送费立减{{shopInfo.shop.reducedDeliveryPrice}}元</view>
      </view>
      <view class="notice-view out_of_range one_row">{{shopInfo.shop.announcement}}</view>
    </view>

  </view>

  <!-- <view class="swiper-tabs-choice self-adaption" style="{{scrollTop?'':'display:none'}}">
		<view class="swiper-tabs-choice-item" wx:for="{{businessModes}}" wx:key="index" data-current="{{index}}" bindtap="clickTab" hover-class='hover-click-class'>
			<view class="swiper_table_item_view {{currentTab==item.modeId?'active':''}}" data-current="{{index}}" bindtap="clickTab">
				{{item.modeName}}
			</view>
		</view>
	</view> -->
  <view id="d_p_s" class="self-adaption position-sticky" style="position: sticky;top: -1;z-index: 1;">
    <view class="swiper-tabs-choice" style="position: sticky;top: 0rpx;">
      <view class="swiper-tabs-choice-item" wx:for="{{businessModes}}" wx:key="index" data-current="{{index}}" bindtap="clickTab" hover-class='hover-click-class'>
        <view class="swiper_table_item_view {{currentTab==item.modeId?'active':''}}" data-current="{{index}}" bindtap="clickTab">
          {{item.modeName}}
        </view>
      </view>
    </view>
  </view>
  <view id="businessRecommend" class="business-recommend-view">
    <view wx:if="{{merchantRecommendGoods.length>0&&currentTab==0}}">
      <view class="business-recommend-title">商家推荐</view>
      <view class="business-recommend-scroll-view">
        <scroll-view scroll-x="true">
          <view class='business-recommend-items' hover-class='hover-view-class'>
            <view class="business-recommend-item {{goodsItem.goodsStatus==4?'isEnd':''}}" wx:for="{{merchantRecommendGoods}}" wx:key="index" wx:for-item="goodsItem">
              <view class="recommend-sell-out" wx:if="{{goodsItem.goodsStatus==4}}">售罄</view>
              <view class="business-recommend-detail-view" bindtap="{{goodsItem.goodsStatus==4?'':'commodityDetailTap'}}" data-id="{{goodsItem.goodsId}}">
                <image src="{{goodsItem.mainImage}}" mode="aspectFill" class="icon-business-recommend-class">
                </image>
              </view>
              <view class="business-recommend-money-view">
                <!-- <view class="business-recommend-money" bindtap="commodityDetailTap" data-id="{{item.id}}">
									<text>￥</text>
									<text>{{item.goodsPrice}}</text>
								</view>
								<view class="plus-view theme-bg" data-goodsId="{{item.goodsId}}" bindtap="{{item.goodsStatus!=4?'openSpecifications':''}}">+</view> -->
                <view class="fullname-class out_of_range one_row">{{goodsItem.goodsName}}</view>
              </view>
              <view class="business-recommend-money-view">
                <view class="fullname-class out_of_range one_row">月售：{{goodsItem.latelyMonthlySales}}</view>
                <text></text>
              </view>
              <view class="business-recommend-money-view fullname-stepper">
                <view class="fullname-class out_of_range one_row">￥{{goodsItem.goodsPrice}}</view>
                <view class='stepper' wx:if="{{goodsItem.number>0}}">
                  <block wx:if="{{!shopInfo.isOutofDeliveryRange&&shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating}}">
                    <text class='flex_center car_reduce_add reduce-class' bindtap='bindMinus' data-cartId="{{goodsItem.cartId}}" data-number="{{goodsItem.number}}">－</text>
                    <input disabled type='number' value='{{goodsItem.number}}' class='radd-reduce-input'></input>
                    <text bindtap='bindPlus' class="flex_center car_reduce_add add-class" data-goodsId="{{goodsItem.goodsId}}" bindtap="{{goodsItem.goodsStatus!=4?'openSpecifications':''}}">＋</text>
                  </block>
                </view>
                <block wx:if="{{goodsItem.number<=0&&!shopInfo.isOutofDeliveryRange&&shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating}}">
                  <view class="insert-view shangjiatuijianjia theme-bg" data-goodsId="{{goodsItem.goodsId}}" bindtap="{{goodsItem.goodsStatus!=4?'openSpecifications':''}}">＋</view>
                </block>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
  <view wx:if="{{currentTab==0}}" class="self-adaption position-sticky" style="height: 93vh;">
    <view wx:if="{{menuList.length>0}}" id="menu_list" class="{{ifScroll?'':'menu_list'}}" style="height: 93vh;">
      <mp-vtabs ifScrolling="{{ifScroll}}" vtabs="{{menuList}}" activeTab="{{activeTab}}" tabBarClass="mp-vtabs-class-1" bindtabclick="onTabCLick" bindchange="onChange" tabBarLineColor="#004ca0" tabBarActiveTextColor="#004ca0" contentHeight="{{contentHeight}}">
        <block wx:for="{{menuList}}" wx:key="title" wx:for-item="menu" wx:for-index="menuIndex">
          <mp-vtabs-content tabIndex="{{menuIndex}}" >
            <view class="vtabs-content-item {{menuList.length-1==menuIndex?'is-end-item':''}}">
              <view class="commodity-type position-sticky">
                <view class="categoryName-view">{{menu.name}}</view>
              </view>
              <block wx:for="{{menu.goodsList}}" wx:key="index" wx:for-index="goodsIndex" wx:for-item="chil">
                <view class="commodity-item {{chil.goodsStatus==4?'isEnd':''}}" hover-class="{{chil.goodsStatus==4?'':'hover-class-public'}}">
                  <view class="commodity-name-view" bindtap="{{chil.goodsStatus==4?'':'commodityDetailTap'}}" data-id="{{chil.goodsId}}">
                    <image src="{{chil.mainImage?chil.mainImage:'../../assets/images/load-image.png'}}" mode="aspectFill" class="commodity-image"></image>
                    <view class="sell-out" hidden="{{chil.goodsStatus!=4}}">售罄</view>
                    <view class="commodity-name-english-view" style="flex: 1">
                      <view class="commodity-name">{{chil.goodsName}}</view>
                      <view class="commodity-english"><text> </text></view>
                      <view class="money-view">￥{{chil.goodsPrice}}</view>
                    </view>
                  </view>
                </view>
                <view class="money-insert-view" wx:if="{{!shopInfo.isOutofDeliveryRange&&shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating}}">
                  <!-- <view class="insert-view theme-bg">＋</view> -->
                  <view class='stepper' wx:if="{{chil.number>0}}">
                    <block>
                      <view class='flex_center car_reduce_add reduce-class' bindtap='bindMinus' data-cartId="{{chil.cartId}}" data-number="{{chil.number}}">－</view>
                      <input disabled type='number' value='{{chil.number}}' class='radd-reduce-input'></input>
                      <view bindtap='bindPlus' class="flex_center car_reduce_add add-class" data-goodsId="{{chil.goodsId}}" bindtap="{{chil.goodsStatus!=4?'openSpecifications':''}}">＋</view>
                    </block>
                  </view>
                  <block wx:else>
                    <view class="insert-view theme-bg {{chil.goodsStatus==4?'isEnd':''}}" data-goodsId="{{chil.goodsId}}" bindtap="{{chil.goodsStatus!=4?'openSpecifications':''}}">＋</view>
                  </block>
                </view>
              </block>
            </view>
          </mp-vtabs-content>
        </block>
      </mp-vtabs>
      <!-- <view id="menu_scorll_mask" class="{{ifScroll?'':'menu_scorll_mask'}}"></view> -->
    </view>
    <is-show-tip isShow="{{menuList.length<=0}}" src="{{noDataTip}}" top="25" bottom="20" text="{{menuList.length<=0}}" tipText="暂无商品"></is-show-tip>
    <view class="shopping-cart-detail" id="shopping-cart-detail">
      <view class="content-fullReductionRuleName">
        <!-- {{fullPriceReductionIsHidden?fullReductionRuleName:'暂无优惠'}} -->
        {{!shopInfo.isOutofDeliveryRange?shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating?fullPriceReductionIsHidden?fullReductionRuleName:'暂无优惠':'商家已休息，暂不接单':'超出配送范围'}}
      </view>
      <view class="shopping-cart-content {{shopInfo.isOutofDeliveryRange||!shopInfo.isOperatingOfShop||!shopInfo.shop.isOperating?'isEnd':''}}">
        <view class="shopping-cart-left" bindtap="{{!shopInfo.isOutofDeliveryRange&&shopInfo.isOperatingOfShop&&shopInfo.shop.isOperating?'openShoppingCart':''}}">
          <view>
            <view class="iconfont icongouwuche1 highlight {{shoppingCartList.length<=0?'theme-other-bg':'theme-bg'}}">
              <view class="num" wx:if="{{totalNum > 0}}">{{totalNum}}</view>
            </view>
          </view>
          <view>
            <view class="shopping-cart-totalPrice">
              <view class="{{fullPriceReductionIsHidden?'fullPriceReductionClass':'totalPrice'}}" wx:if="{{shoppingCartList.length>0}}">￥{{totalPrice}}</view>
              <view class="full-price-reduction" wx:if="{{fullPriceReductionIsHidden&&shoppingCartList.length>0}}">￥{{fullPriceReduction}}</view>
              <view class="not-full-price-reduction" wx:if="{{shoppingCartList.length<=0}}">暂未选购商品</view>

            </view>
            <!-- <view class="shopping-cart-desc">另需配送费￥{{deliveryPrice}}元</view> -->
          </view>
        </view>
        <view class="{{isStartDeliveryPrice?'theme-bg':'shopping-cart-bg'}} shopping-cart-right" bindtap="{{shoppingCartList.length<=0||!isStartDeliveryPrice||shopInfo.isOutofDeliveryRange||!shopInfo.isOperatingOfShop||!shopInfo.shop.isOperating?'':'goToPay'}}">
          {{isStartDeliveryPrice?"去结算":"差 ￥"+priceDifference+" 起送"}}
        </view>
      </view>
      <mp-halfScreenDialog show="{{shoppingCartDialog}}" extClass="extClassShoppingCart" bind:close="closeShoppingCart">
        <view slot="title">已选商品</view>
        <view slot="desc">
          <scroll-view style="height: 55vh;" scroll-y>
            <view class="shoppingCart-item" wx:for="{{shoppingCartList}}" wx:key="key">
              <view class="goodsName-restructure-view">
                <view class="goodsName out_of_range one_row">{{item.goodsName}}</view>
                <view class="restructure out_of_range one_row">{{item.restructure}}</view>
              </view>
              <view class="goodsPrice-number-view">
                <view class="goodsPrice">￥{{item.price}}</view>
                <view class='stepper'>
                  <view class='flex_center car_reduce_add reduce-class' bindtap='bindMinus' data-cartid="{{item.id}}" data-number="{{item.number}}">－</view>
                  <input disabled type='number' value='{{item.number}}' class='add-reduce-input'></input>
                  <view bindtap='bindPlus' class="flex_center car_reduce_add add-class" data-num="{{index}},{{item.number}}">＋</view>
                </view>
              </view>
            </view>
            <view class="goodsName-packingCharges">
              <text class="goodsName">包装费</text>
              <text class="goodsPrice">￥{{packingCharges}}</text>
              <text></text>
            </view>
          </scroll-view>
        </view>
      </mp-halfScreenDialog>
      <mp-halfScreenDialog show="{{specificationsDialog}}" extClass="extClassSpecifications">
        <view slot="title">选择规格</view>
        <view slot="desc">
          <view class="goods-info-view">
            <image src="{{goodsInfo.mainImage}}" mode="aspectFill" class="commodity-image"></image>
            <view>
              <view class="goods-info-name">{{goodsInfo.name}}</view>
              <view class="goods-info-specListString">已选：{{specListString}}</view>
              <view class="goods-info-price">￥{{priceAfter}}</view>
            </view>
          </view>
          <scroll-view scroll-y style="height: 56vh;">
            <view class="commdity-name-type-view">
              <view class="commdity-name">{{data.name}}</view>
              <view class="commdity-engname">{{data.name}}</view>
              <view class="commdity-type-item" wx:for="{{specList}}" wx:key="index" wx:for-index='key'>
                <view class="commdity-type-name">{{key}}</view>
                <radio-group class="radio-group" bindchange="radioChange" data-firstIndex="{{key}}">
                  <label wx:for="{{item}}" wx:key="index" class="group-label theme-border {{!item.stock?'disabled-group-label':''}} {{item.checked?'active theme-bg':'theme-color-border'}} out_of_range one_row">
                    <radio value="{{index}}" checked="{{item.checked}}" disabled="{{!item.stock}}" class="radio" /> {{item.name}}
                  </label>
                </radio-group>
              </view>
            </view>
          </scroll-view>
          <view slot="footer">
            <view class="good-choice-btn theme-bg" bindtap="insertShoppingCart">我选好了</view>
          </view>
        </view>
      </mp-halfScreenDialog>
    </view>
  </view>

  <view wx:if="{{currentTab==1}}">
    <view class="evaluate-business-info">
      <view class="evaluate-info-left">
        <view class="evaluate-business-info evaluate-info-left-business">
          <view class="evaluate-business-info business-evaluate">
            <view class="evaluate-total-score">{{shopInfo.shop.serviceRating}}</view>
            <view class="evaluate-total-star">
              <view>商家评分</view>
              <view>
                <multiple-rate rate="{{shopInfo.shop.serviceRating>4&&shopInfo.shop.serviceRating<5}}" disabled="{{disabled}}">
                </multiple-rate>
              </view>
            </view>
          </view>
          <!-- <view>
						<view class="evaluate-total-star">味道</view>
						<view class="evaluate-total-num">5.0</view>
					</view>
					<view>
						<view class="evaluate-total-star">包装</view>
						<view class="evaluate-total-num">5.0</view>
					</view> -->
        </view>
      </view>
      <!-- <view class="evaluate-info-right">
				<view class="evaluate-total-star">配送</view>
				<view class="evaluate-total-num">5.0</view>
			</view> -->
    </view>
    <view class="view-line"></view>
    <view class="evaluate-items">
      <view class="evaluate-item" wx:for="{{appraiseList}}" wx:key="index" wx:for-index="appraiseIndex">
        <image src="{{item.headImg}}" class="evaluate-user-image" mode="widthFix"></image>
        <view class="evaluate-item-detail">
          <view class="evaluate-itemu-username-time">
            <text class="username-detail out_of_range one_row">{{item.username}}</text>
            <text class="datetime-detail">{{item.createTime}}</text>
          </view>
          <multiple-rate rate="{{item.level}}" disabled="{{disabled}}">
          </multiple-rate>
          <view class="username-detail">
            {{item.content}}
          </view>
          <view wx:if="{{item.imagesUrls.length>0}}">
            <block wx:for="{{item.imagesUrls}}" wx:key="index" wx:for-item="img">
              <image src="{{img}}" data-index="{{index}}" data-imgs="{{item.imagesUrls}}" bindtap="viewImage" class="images-url" mode="aspectFill"></image>
            </block>
          </view>
          <view class="pl-dz-class">
            <text></text>
            <view class="pl-dz-view">
              <view class="pl-dz-display" data-appraiseid="{{item.id}}" data-username="{{item.username}}" data-appraiseindex="{{appraiseIndex}}" bindtap="appraiseTap">
                <text class="iconfont iconpinglun"></text>
                <text decode="true" class="appraise-class-pl-dz">评论&nbsp;&nbsp;</text>
              </view>
              <view class="pl-dz-display" bindtap="{{item.isGiveLike==1?'deleteLikeCountTap':'giveLikeCountTap'}}" data-appraiseid="{{item.id}}" data-type="1" data-appraiseindex="{{appraiseIndex}}">
                <text class="iconfont icondianzan {{item.isGiveLike==1?'theme-color':''}}"></text>
                <text class="appraise-class-pl-dz {{item.isGiveLike==1?'theme-color':''}}">{{item.giveLikeCount>0?'点赞'+item.giveLikeCount:'点赞'}}</text>
              </view>
            </view>
          </view>
          <view class="appraise-reply-items" wx:if="{{item.replyPageInfo.list.length>0}}" bind:tap="handleTap">
            <view wx:for="{{item.replyPageInfo.list}}" class="reply-item list-item" wx:key="placement">

              <mp-select-text show-copy-btn placement="{{item.placement}}" value="{{item.answeredUsername?item.username+' 回复 '+item.answeredUsername:item.username}}：{{item.content}}" data-username="{{item.username}}" data-appraiseid="{{item.appraiseId}}" data-replyid="{{item.id}}" data-type="2" data-appraiseindex="{{appraiseIndex}}" on-document-tap="{{evt}}" bindtap="replyTap" bindcopy="handleTap" bindcurrency="handleCurrencyTap" selectTexts="{{item.selectTexts}}">
              </mp-select-text>
            </view>

          </view>
        </view>
      </view>
    </view>

  </view>

  <view class="swiper-bussiness" wx:if="{{currentTab==2}}">
    <!-- <view class="commodity-menu-view" style="{{scrollTop?marginTopMenu:''}}"> -->
    <scroll-view class="swiper-tab scroll-views swiper-tab-bussiness" scroll-y>
      <!-- <view class="swiper-bussiness-item">
				<view class="swiper-bussiness-title">配送信息</view>
				<view class="swiper-bussiness-info">
					<text class="theme-bg">饿了么专送</text>
					<view>约40分钟送达，距离897m</view>
				</view>
				<view class="swiper-bussiness-info">配送费￥1</view>
			</view> -->
      <view class="swiper-bussiness-item" wx:if="{{shopInfo.shop.shopWithinImgs.length>0}}">
        <view class="swiper-bussiness-title">商家实景</view>
        <swiper indicator-dots="{{indicatorDots}}" class="carousel-swiper" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" indicator-active-color="{{afterColor}}" style="margin-top:10rpx;">
          <block wx:for="{{shopInfo.shop.shopWithinImgs}}" wx:key="index">
            <swiper-item class="carousel-swiper-item">
              <image src="{{item}}" class="carousel-image" mode='scaleToFill' data-imageLinkUrl="{{item}}" />
            </swiper-item>
          </block>
        </swiper>
      </view>
      <view class="swiper-bussiness-item">
        <view class="swiper-bussiness-title">商家信息</view>
        <view class="swiper-bussiness-info">
          <view class="swiper-bussiness-row">
            <text>{{shopInfo.shop.briefIntroduction?shopInfo.shop.briefIntroduction:'暂无简介'}}</text>
          </view>

        </view>
      </view>
      <view class="view-line"></view>
      <view class="swiper-bussiness-row">
        <text class="swiper-bussiness-row-left">品类</text>
        <text>{{shopInfo.shop.managePrimary}}</text>
      </view>
      <view class="view-line"></view>
      <view class="swiper-bussiness-row" bindtap="contactBussinessTap">
        <text class="swiper-bussiness-row-left">商家电话</text>
        <text class="contact-bussiness-text">联系商家</text>
      </view>
      <view class="view-line"></view>
      <view class="swiper-bussiness-row">
        <text class="swiper-bussiness-row-left">地址</text>
        <view>
          <view>{{shopInfo.shop.province?shopInfo.shop.province:''}}{{shopInfo.shop.city?shopInfo.shop.city:''}}
          {{shopInfo.shop.area?shopInfo.shop.area:''}}</view>
          <view>{{shopInfo.shop.street}}</view>
        </view>
      </view>
      <view class="view-line"></view>
      <view class="swiper-bussiness-row">
        <text class="swiper-bussiness-row-left">营业时间</text>
        <text>{{shopInfo.shop.startTime}}-{{shopInfo.shop.endTime}}</text>
      </view>
      <view class="view-line"></view>
      <view class="swiper-bussiness-item">
        <view class="swiper-bussiness-title">商家资质</view>
			  <image src="{{shopInfo.shop.businessLicense}}" class="uploader-imgs-item" mode="aspectFill"></image>
      </view>
    </scroll-view>
  </view>
</view>
<view class="pinglun-bottom" wx:if="{{currentTab==1&&appraiseFocus}}">
  <textarea class="userinfo-text input-appraise" focus="{{appraiseFocus}}" show-confirm-bar="{{showConfirmBar}}" bindblur="bindblur" placeholder="回复 {{userName}}" bindinput="bindInputAppraise" cursor-spacing="152" bindconfirm="bindconfirm" confirm-type="send" value="{{content}}" fixed="{{!showConfirmBar}}"></textarea>
  <view class="reply-button-view">
    <button size="mini" class="theme-bg" bindtap="bindconfirm">回复</button>
  </view>
</view>
<mp-halfScreenDialog show="{{isActivityDialog}}" extClass="extClassSpecifications">
  <view slot="title">优惠活动</view>
  <view slot="desc">
    <scroll-view style="height: 55vh;" scroll-y>
      <view wx:if="{{shopInfo.promotionList.length>0}}">
        <text class="dialog-title">优惠：</text>
        <view class="business-discount-info business-info-flex">
          <view class="business-discount out_of_range one_row" bindtap="isPromotionTap">
            <view class="theme-color-border business-discount-list" wx:for="{{shopInfo.promotionList}}" wx:key="index" wx:if="{{index<=5}}" wx:for-item="rule">
              {{rule.name}}
            </view>
          </view>
        </view>
      </view>
      <view class="reduced-delivery-price" wx:if="{{shopInfo.shop.reducedDeliveryPrice}}">
        <text class="dialog-title">配送费：</text>配送费立减{{shopInfo.shop.reducedDeliveryPrice}}元
      </view>
      <view class="reduced-delivery-price" wx:if="{{shopInfo.shop.announcement}}">{{shopInfo.shop.announcement}}</view>
    </scroll-view>
  </view>
</mp-halfScreenDialog>
<mp-halfScreenDialog show="{{isOutofDeliveryRangeDialog}}" extClass="extClassSpecifications">
  <view slot="title">本店超出配送范围</view>
  <view slot="desc">
    <view class="edit-address-class">
      建议您更换地址再下单
    </view>
  </view>
  <view slot="footer">
    <button size="mini" class="edit-address-btn theme-bg" bindtap="editAddress" style="font-size:24rpx;">更换地址</button>
  </view>
</mp-halfScreenDialog>
<!-- <view class="pinglun-bottom" wx:if="{{currentTab==1&&appraiseFocus}}">
	<input class="userinfo-text input-appraise" focus="{{appraiseFocus}}" bindblur="bindblur" placeholder="评论" bindinput="bindInputAppraise" bindconfirm="bindconfirm" confirm-type="send" value="{{content}}"></input>
</view> -->

<!-- <mp-dialog show="{{isShopDetail}}" buttons="{{buttons}}" mask-closable="{{maskClosable}}" catchtouchmove="true"
ext-class="{{extClassShopDetail}}" bind:tap="close">
	<view class="shop-detail-dialog">
		<view>
			<view></view>
		</view>
		<view>公告</view>
		<view>
			{{shopInfo.shop.announcement}}
		</view>
	</view>
</mp-dialog> -->