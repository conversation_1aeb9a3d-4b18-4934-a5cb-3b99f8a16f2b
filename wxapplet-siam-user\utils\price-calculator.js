import utilHelper from './util';

/**
 * 统一的价格计算器
 * 负责处理所有与价格相关的计算逻辑，确保计算的准确性和一致性
 */
class PriceCalculator {
  constructor() {
    this.reset();
  }

  /**
   * 重置所有价格数据
   */
  reset() {
    this.basePrice = 0;              // 商品基础价格
    this.packingCharges = 0;         // 包装费
    this.deliveryFee = 0;            // 配送费
    this.upstairsFee = 0;            // 上楼费
    this.couponDiscount = 0;         // 优惠券折扣金额
    this.fullReductionDiscount = 0;  // 满减折扣金额
    this.couponInfo = null;          // 优惠券信息
    this.fullReductionInfo = null;   // 满减信息
    this.isDelivery = true;          // 是否配送（false为自取）
    this.isUpstairs = false;         // 是否上楼
  }

  /**
   * 设置基础价格（商品总价）
   */
  setBasePrice(price) {
    this.basePrice = this.toFixed(price);
    return this;
  }

  /**
   * 设置包装费
   */
  setPackingCharges(charges) {
    this.packingCharges = this.toFixed(charges);
    return this;
  }

  /**
   * 设置配送方式
   */
  setDeliveryMode(isDelivery) {
    this.isDelivery = isDelivery;
    if (!isDelivery) {
      this.deliveryFee = 0;
      this.upstairsFee = 0;
      this.isUpstairs = false;
    }
    return this;
  }

  /**
   * 设置配送费
   */
  setDeliveryFee(fee) {
    this.deliveryFee = this.isDelivery ? this.toFixed(fee) : 0;
    return this;
  }

  /**
   * 设置上楼选项
   */
  setUpstairsOption(isUpstairs, upstairsFee = 0) {
    this.isUpstairs = this.isDelivery ? isUpstairs : false;
    this.upstairsFee = (this.isDelivery && isUpstairs) ? this.toFixed(upstairsFee) : 0;
    return this;
  }

  /**
   * 设置优惠券信息
   */
  setCoupon(couponInfo) {
    if (!couponInfo || !couponInfo.id) {
      this.couponDiscount = 0;
      this.couponInfo = null;
      return this;
    }

    this.couponInfo = couponInfo;
    this.couponDiscount = this.calculateCouponDiscount(couponInfo);
    return this;
  }

  /**
   * 设置满减信息
   */
  setFullReduction(fullReductionInfo) {
    this.fullReductionInfo = fullReductionInfo;
    this.fullReductionDiscount = this.calculateFullReductionDiscount();
    return this;
  }

  /**
   * 计算优惠券折扣
   */
  calculateCouponDiscount(couponInfo) {
    if (!couponInfo || !couponInfo.price) return 0;
    
    // 优惠券折扣不能超过商品基础价格
    const maxDiscount = this.basePrice + this.packingCharges;
    const discount = Math.min(couponInfo.price, maxDiscount);
    return this.toFixed(discount);
  }

  /**
   * 计算满减折扣
   */
  calculateFullReductionDiscount() {
    if (!this.fullReductionInfo || !this.fullReductionInfo.reducedPrice) return 0;

    // 计算满减基础金额（商品价格 + 包装费 - 优惠券折扣）
    const baseForFullReduction = this.basePrice + this.packingCharges - this.couponDiscount;
    
    // 检查是否满足满减条件
    if (baseForFullReduction < this.fullReductionInfo.limitedPrice) {
      return 0;
    }

    // 满减折扣不能超过基础金额
    const maxDiscount = baseForFullReduction;
    const discount = Math.min(this.fullReductionInfo.reducedPrice, maxDiscount);
    return this.toFixed(discount);
  }

  /**
   * 计算最终价格
   */
  calculateFinalPrice() {
    let finalPrice = this.basePrice;           // 商品基础价格
    finalPrice += this.packingCharges;         // 加上包装费
    finalPrice -= this.couponDiscount;         // 减去优惠券折扣
    finalPrice -= this.fullReductionDiscount;  // 减去满减折扣
    finalPrice += this.deliveryFee;            // 加上配送费
    finalPrice += this.upstairsFee;            // 加上上楼费

    // 确保最终价格不为负数
    return Math.max(0, this.toFixed(finalPrice));
  }

  /**
   * 获取价格详情
   */
  getPriceDetails() {
    return {
      basePrice: this.basePrice,                    // 商品基础价格
      packingCharges: this.packingCharges,          // 包装费
      deliveryFee: this.deliveryFee,                // 配送费
      upstairsFee: this.upstairsFee,                // 上楼费
      couponDiscount: this.couponDiscount,          // 优惠券折扣
      fullReductionDiscount: this.fullReductionDiscount, // 满减折扣
      subtotal: this.toFixed(this.basePrice + this.packingCharges), // 小计
      totalDiscount: this.toFixed(this.couponDiscount + this.fullReductionDiscount), // 总折扣
      finalPrice: this.calculateFinalPrice(),       // 最终价格
      isDelivery: this.isDelivery,                  // 是否配送
      isUpstairs: this.isUpstairs,                  // 是否上楼
      couponInfo: this.couponInfo,                  // 优惠券信息
      fullReductionInfo: this.fullReductionInfo     // 满减信息
    };
  }

  /**
   * 获取用于支付的金额（兼容原有逻辑）
   */
  getPaymentAmount() {
    return this.calculateFinalPrice();
  }

  /**
   * 获取显示用的价格信息（兼容原有data结构）
   */
  getDisplayData() {
    const details = this.getPriceDetails();
    return {
      actualPrice: details.finalPrice,
      fullPriceReduction: details.finalPrice,
      fullPriceReductionIsHidden: details.fullReductionDiscount > 0,
      couponsIsHidden: details.couponDiscount > 0,
      packingCharges: details.packingCharges,
      basePrice: details.basePrice,
      totalDiscount: details.totalDiscount
    };
  }

  /**
   * 数值精度处理
   */
  toFixed(value, precision = 2) {
    if (typeof value !== 'number') {
      value = parseFloat(value) || 0;
    }
    return utilHelper.toFixed(value, precision);
  }

  /**
   * 验证价格数据的合理性
   */
  validate() {
    const errors = [];
    
    if (this.basePrice < 0) {
      errors.push('商品基础价格不能为负数');
    }
    
    if (this.deliveryFee < 0) {
      errors.push('配送费不能为负数');
    }
    
    if (this.couponDiscount < 0) {
      errors.push('优惠券折扣不能为负数');
    }
    
    if (this.fullReductionDiscount < 0) {
      errors.push('满减折扣不能为负数');
    }

    const finalPrice = this.calculateFinalPrice();
    if (finalPrice < 0) {
      errors.push('最终价格不能为负数');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 克隆当前计算器状态
   */
  clone() {
    const newCalculator = new PriceCalculator();
    newCalculator.basePrice = this.basePrice;
    newCalculator.packingCharges = this.packingCharges;
    newCalculator.deliveryFee = this.deliveryFee;
    newCalculator.upstairsFee = this.upstairsFee;
    newCalculator.couponDiscount = this.couponDiscount;
    newCalculator.fullReductionDiscount = this.fullReductionDiscount;
    newCalculator.couponInfo = this.couponInfo ? { ...this.couponInfo } : null;
    newCalculator.fullReductionInfo = this.fullReductionInfo ? { ...this.fullReductionInfo } : null;
    newCalculator.isDelivery = this.isDelivery;
    newCalculator.isUpstairs = this.isUpstairs;
    return newCalculator;
  }

  /**
   * 调试信息
   */
  getDebugInfo() {
    const details = this.getPriceDetails();
    const validation = this.validate();
    
    return {
      ...details,
      validation: validation,
      calculation: {
        step1_base: this.basePrice,
        step2_withPacking: this.toFixed(this.basePrice + this.packingCharges),
        step3_withCoupon: this.toFixed(this.basePrice + this.packingCharges - this.couponDiscount),
        step4_withFullReduction: this.toFixed(this.basePrice + this.packingCharges - this.couponDiscount - this.fullReductionDiscount),
        step5_withDelivery: this.toFixed(this.basePrice + this.packingCharges - this.couponDiscount - this.fullReductionDiscount + this.deliveryFee),
        step6_final: this.calculateFinalPrice()
      }
    };
  }
}

export default PriceCalculator;
