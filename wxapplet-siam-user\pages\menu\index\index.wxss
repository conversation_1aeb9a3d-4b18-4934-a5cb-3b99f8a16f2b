/* page {
  padding-bottom: 40rpx;
} */

.content-class {
  /* margin-top: 6%; */
  width: 100%;
  height: 100%;
}

/* 地址定位 */
.store-full-name-view {
  box-shadow: -2px 0.5px 5px 0.5px rgba(0, 0, 0, 0.1);
  padding: 15rpx 20rpx;
  margin: 15rpx 20rpx;
  border-radius: 10rpx;
  /* display: flex;
  justify-content: space-between;
  align-items: center; */
  background: #fff;
}

.banner-view {
  margin: 10rpx 20rpx;
  border-radius: 10rpx;
}

.place-image {
  width: 10%;
  height: auto;
  padding-right: 20rpx;
}

.business-text {
  font-size: 24rpx;
  font-weight: bold;
}

.right-class {
  width: 90%;
}

.store-full-name-place-view {
  font-size: 26rpx;
  font-weight: bold;
}

.distance-phone-username-veiw {
  color: #6b6b6b;
  font-size: 24rpx;
  line-height: 42rpx;
}

.tips-view {
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.mbp-view {
  color: #a1a1a1;
}

.bar- {
  font-size: 28rpx;
  margin: 0 10rpx;
}

/* 单选框样式--自取配送 */
.radio-group-view {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 25%;
}

.radio-group {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 5rpx;
  border-radius: 50rpx;
  /* height: 66rpx; */
}

.radio-group-label-radio {
  display: none;
}

.radio-group-label {
  width: 46%;
  padding: 2%;
  font-size: 26rpx;
  border-radius: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  /* height: 60rpx; */
}

.not-active {
  color: white;
}

/* 轮播图样式 */
swiper {
  width: 100%;
  height: 366rpx;
}

.slide-image {
  width: 100%;
  height: 366rpx;
  border-radius: 10rpx;
}

.business-view {
  /* position: absolute;
  top: 6%; */
  text-align: center;
  width: 100%;
  margin-top: -20%;
}

.business-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx solid #f5f5f5;
}

.business-info {
  /* margin-top: 40rpx; */
  text-align: center;
  margin-left: 20px;
  margin-right: 20px;
}

.business-info-name {
  font-size: 36rpx;
  font-weight: bold;
}

.notice-view {
  font-size: 24rpx;
  color: #9b9b9b;
  margin: 20rpx;
}

.business-info-other {
  font-size: 22rpx;
}

.swiper-tabs-choice {
  width: 100%;
  text-align: center;
  height: 88rpx;
  line-height: 88rpx;
  display: flex;
  flex-flow: row;
  justify-content: space-between;
  background: #fff;
  z-index: 1;
  border-bottom: 6rpx solid #f5f5f5;
}

.swiper-tabs-choice-item {
  width: 50%;
}

.swiper-items {
  height: 100%;
}

/* 菜单品类样式 */
.swiper-tab {
  /* width: 24%; */
  text-align: center;
  flex-flow: row;
  justify-content: space-between;
  z-index: -1;
  font-size: 34rpx;
}

.swiper-tab-item {
  /* width: 100%; */
  /* line-height: 88rpx; */
  /* color:#969696; */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10rpx 15rpx;
  font-size: 28rpx;
  height: 80rpx;
  font-weight: bold;
}

.swiper_table_item_view {
  display: flex;
  align-items: center;
  justify-content: center;
  /* border-bottom: 4rpx solid #fff; */
}

.swiper-box {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.swiper-active {
  background: white;
  transition: .5s;
}

.swiper-items {
  height: 100%;
}

.selectMenuTap {
  width: 40%;
}

.scroll-views {
  height: 100%;
  background: #f5f5f5;
  border-radius: 10rpx;
}

.full-reduction-view {
  display: flex;
  align-items: center;
  background: white;
  padding: 10rpx 0rpx;
  height: 34rpx;
}

.full-reduction-list {
  font-size: 24rpx;
  font-weight: bold;
  padding: 0 10rpx;
  margin: 0 5rpx;
  border-radius: 10rpx;
}

.full-reduction-text {
  color: white;
  margin-left: 20rpx;
  font-size: 28rpx;
  width: 10%;
  text-align: center;
  border-radius: 15rpx;
}

.business-discount-list {
  padding: 1rpx 6rpx;
  font-size: 20rpx;
  font-weight: bold;
  border-radius: 10rpx;
  margin-right: 5rpx;
}

.other-promotionList{
  width: 27%;
  display: flex;
  align-items: center;
}

.business-discount {
  width: 85%;
  padding-bottom: 1rpx;
  margin-bottom: 10rpx;
  display: flex;
  align-items: center;
}

.business-info-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 24rpx;
  color: #717171;
  margin: 10rpx 10rpx 10rpx 0;
}

.commodity-menu-view {
  box-shadow: -2px 0.5px 5px 0.5px rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  justify-content: space-between;
  border-radius: 10rpx;
  padding-bottom: 73px;
}

.commodity-detail-view {
  width: 100%;
  /* background: white; */
  border-radius: 10rpx;
}

/* 商品信息 */
.commodity-item-view {
  display: flex;
  flex-direction: column;
  /* margin-bottom: 10rpx; */
  background: white;
  border-radius: 10rpx;
  padding-top: 20rpx;
}

.commodity-type {
  line-height: 64rpx;
  padding: 0rpx 20rpx;
  /* margin-top: 25rpx; */
}

.categoryName-view {
  /* width: 35%; */
  font-size: 28rpx;
}

.font-white {
  color: white;
}

.commodity-item {
  display: flex;
  padding: 10rpx 20rpx 10rpx 20rpx;
  align-items: center;
  border-radius: 5rpx;
  background-color: white;
}

.commodity-image {
  width: 170rpx;
  height: 170rpx;
  border-radius: 8rpx;
  margin-right: 10rpx;
}

.commodity-name-view {
  width: 100%;
  display: flex;
  align-items: center;
}

.line-view {
  background: #b0b0b0;
  width: 100%;
  height: 2rpx;
}

.commodity-name {
  font-size: 28rpx;
  color: #969696;
  font-weight: bold;
}

.commodity-english {
  font-size: 24rpx;
  color: #b0b0b0;
}

.money-view {
  font-size: 30rpx;
  font-weight: bold;
  margin-top: 30rpx;
}

.insert-view {
  border-radius: 50%;
  width: 45rpx;
  height: 45rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  line-height: 45rpx;
}

.shangjiatuijianjia{
  margin-right: 35rpx;
}

.money-insert-view {
  /* width: 100%; */
  position: absolute;
  /* margin-left: 85%;
  margin-top: 10%; */
  margin-top: -10%;
  right: 20rpx;
}

#space-view {
  height: 20rpx;
  background: white;
}

.now-order-image {
  width: 100%;
  height: 100%;
}

/* shopcart 样式 */
.content-fullReductionRuleName {
  width: 100%;
  background: #fffadc;
  font-size: 24rpx;
  text-align: center;
  position: sticky;
  top: 0;
  z-index: 0;
  opacity: 0.7;
  visibility: visible;
  line-height: 40rpx;
}

.shopping-cart-detail {
  position: fixed;
  bottom: 0;
  z-index: 9999;
  width: 100%;
  background: white;
}

.highlight {
  position: relative;
  top: -10px;
  width: 80rpx;
  line-height: 80rpx;
  text-align: center;
  height: 80rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  border-radius: 50%;
  border: 10rpx solid #444444;
}

.shopping-cart-content {
  width: 100%;
  display: flex;
  align-items: center;
  background: #535257;
  z-index: 9999;
  height: 54px;
}

.shopping-cart-left {
  width: 70%;
  height: 100%;
  display: flex;
  align-items: center;
  background: #505052;
  z-index: 9999;
}

.shopping-cart-right {
  width: 30%;
  height: 100%;
  text-align: center;
  color: white;
  z-index: 9999;
  font-size: 30rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.shopping-cart-bg{
  background: #535257;
}

.shopping-cart-desc {
  font-size: 24rpx;
  color: #8c8c8e;
}

.shopping-cart-totalPrice {
  display: flex;
  align-items: center;
}

.fullPriceReductionClass {
  color: gainsboro;
  text-decoration: line-through;
  margin-right: 20rpx;
}

.totalPrice {
  color: white;
  font-weight: bold;
}

.full-price-reduction {
  color: white;
  font-weight: bold;
}

.not-full-price-reduction {
  font-size: 28rpx;
  color: #8c8c8e;
}

.fullPriceReductionIsHidden {
  font-size: 28rpx;
}

.content-manjian {
  position: relative;
  top: 0;
  background: #fffadc;
  font-size: 24rpx;
  font-weight: bold;
  text-align: center;
  height: 73px;
  z-index: 9999;
}

.num {
  position: absolute;
  top: -6px;
  right: -10px;
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 16px;
  font-size: 9px;
  font-weight: 700;
  color: #fff;
  background: rgb(240, 20, 20);
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.4);
}

.business-recommend-scroll-view {
  height: 269rpx;
  white-space: nowrap;
}

.icon-business-recommend-class {
  width: 255rpx;
  height: 200rpx;
  border-radius: 15rpx 15rpx 0 0;
}

.business-recommend-title {
  font-size: 34rpx;
  font-weight: bold;
  margin: 20rpx 20rpx 0 20rpx;
}

.business-recommend-scroll-view {
  margin-top: 20rpx;
  padding-bottom: 20rpx;
  background: #fff;
  border-radius: 50rpx;
  height: 100%;
  display: flex;
  align-items: center;
}

.business-recommend-scroll-view scroll-view {
  display: block;
  width: 100%;
}

.business-recommend-items {
  width: 100%;
  display: flex;
  /* justify-content: space-between; */
}

.business-recommend-item {
  width: 255rpx;
  height: auto;
  background: #f5f5f5;
  border-radius: 15rpx;
  margin: 0 10rpx;
}

.item-two {
  margin: 0 3.5%;
}

.recommend-sell-out {
  position: absolute;
  top: 10%;
  margin-left: 10%;
  height: 100rpx;
  line-height: 100rpx;
  width: 100rpx;
  text-align: center;
  opacity: 0.7;
  border-radius: 50%;
  font-size: 30rpx;
  background: #434343;
  color: white;
}
  

.business-recommend-detail-view {
  width: 255rpx;
  height: auto;
}

.fullname-class {
  font-size: 26rpx;
}

.engname-class {
  font-size: 24rpx;
  color: #ccc;
  width: 90%;
}

.business-recommend-money-view {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0rpx 15rpx;
}

.fullname-stepper{
  margin-bottom: 10rpx;
}

.business-recommend-money-view .stepper{
  margin-right: 30rpx;
}

.business-recommend-money {
  font-size: 26rpx;
  font-weight: bold;
}

.plus-view {
  font-size: 28rpx;
  width: 38rpx;
  height: 38rpx;
  line-height: 38rpx;
  text-align: center;
  border-radius: 50%;
  color: white;
}

.settlement-view {
  position: fixed;
  z-index: 999;
  background: white;
  top: 0;
  border-bottom: 6rpx solid #f5f5f5;
}

.manjiantop {
  position: absolute;
  top: 0;
}

.closeImages {
  position: relative;
  left: 91%;
  top: 4%;
  z-index: 999;
}

.close-image-class {
  width: 50rpx;
  height: auto;
}

.goods-info-view {
  display: flex;
  padding-bottom: 20rpx;
  padding-left: 20rpx;
}

.goods-info-name {
  font-size: 30rpx;
  font-weight: bold;
}

.goods-info-specListString {
  color: #6b6b6b;
  font-size: 24rpx;
}

.goods-info-price {
  font-weight: bold;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  color: #e0583b;
}

.specifications-scroll-view {
  height: 274px;
}

.clearNull {
  font-weight: 700;
  font-size: 15px;
  color: #80858a;
}

.specifications-dialog {
  margin-bottom: 0rpx;
}

.commdity-name-type-view {
  padding: 20rpx;
  background: #fff;
}

.commdity-name {
  font-size: 32rpx;
  font-weight: bold;
  line-height: 50rpx;
}

.commdity-engname {
  font-size: 28rpx;
  line-height: 60rpx;
}

.commdity-type-item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: left;
  align-items: center;
  flex-wrap: wrap;
  padding-bottom: 10rpx;
}

.commdity-type-name {
  font-size: 28rpx;
  margin-right: 30rpx;
}

.radio-group {
  width: 80%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding: 10rpx 10rpx 10rpx 0;
  background: #fff;
  border-radius: 50rpx;
}

.group-label {
  width: 28%;
  padding: 1%;
  margin: 1%;
  font-size: 26rpx;
  border-radius: 18rpx;
  text-align: center;
}

.disabled-group-label {
  background: #f5f5f5;
  color: #808080;
  border: none;
}

.radio {
  display: none;
}

.good-choice-view {
  padding: 20rpx;
  border-top: 1prx #808080 solid;
}

.good-choice-btn {
  width: 100%;
  text-align: center;
  padding: 20rpx 0;
  border-radius: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.shoppingCart-screen-dialog {
  height: 55%;
}

.shoppingCart-scroll-view {
  height: 350px;
}

.shoppingCart-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* width: 100%; */
  padding: 20rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.goodsName-restructure-view {
  width: 50%;
}

.goodsName-packingCharges {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
}

.goodsName {
  font-size: 28rpx;
}

.restructure {
  font-size: 22rpx;
  color: #808080;
}

.goodsPrice-number-view {
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goodsPrice {
  font-size: 30rpx;
  font-weight: bold;
  color: #f01414;
}

/*主容器*/

.stepper {
  margin-left: 20rpx;
  display: flex;
  align-items: center;
}

/*加号和减号*/

.stepper text {
  width: 45rpx;
  height: 45rpx;
  line-height: 45rpx;
  font-size: 28rpx;
}

/* 商家栏样式 */
.swiper-bussiness {
  text-align: left;
}

.swiper-tab-bussiness {
  text-align: left;
}

.swiper-bussiness-item {
  background: white;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.swiper-bussiness-info {
  font-size: 28rpx;
  color: #717171;
}

.swiper-bussiness-title {
  font-size: 30rpx;
  font-weight: bold;
}

.swiper-bussiness-row {
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 20rpx;
  background: white;
}

.swiper-bussiness-row-left {
  width: 20%;
  font-size: 26rpx;
  color: black;
  font-weight: bold;
}

.contact-bussiness-text {
  color: #2e87cd;
}

.carousel-swiper-item {
  border-radius: 15rpx;
  height: 200rpx;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassShoppingCart {
  max-height: 100vh;
  padding: 0 20rpx;
  position: fixed;
  bottom: 0;
  padding-bottom: 12%;
}

.weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__ft {
  padding: 20rpx 0;
  position: sticky;
  bottom: 0;
}


.weui-show .weui-half-screen-dialog.extClassShoppingCart .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

/* 选择商品规格弹窗 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  z-index: 9999999;
}

/* 自定义弹窗样式 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  padding: 0 20rpx;
}

.weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__ft {
  padding: 20rpx 0 0 0;
  position: sticky;
  bottom: 0;
}

/* 自定义弹出框的最大高度为100%，并设置他的左右上交的border-ric为0 */
.weui-show .weui-half-screen-dialog.extClassSpecifications {
  max-height: 90vh;
  /* border-radius: 0%; */
}

.weui-show .weui-half-screen-dialog.extClassSpecifications .weui-half-screen-dialog__hd {
  padding: 0 20rpx;
}

.vtabs-content-item {
  height: 100%;
}

/* .mp-vtabs-class scroll-view {
  height: 82%;
  padding-bottom: 11vh;
} */

.weui-vtabs-content__wrp scroll-view {
  /* height: 100vh; */
  padding-bottom: 12vh;
}

.weui-vtabs-bar__scrollview .weui-vtabs-bar__content {
  padding-bottom: 12vh;
  background: #eeeeee;
}

.weui-vtabs-content__scrollview .weui-vtabs-content {
  padding-bottom: 80px;
}

.is-end-item {
  background-color: white;
  padding-bottom: 50px;
}

.theme-other-bg {
  background: #353535;
  color: #5f5e63;
}

.weui-vtabs-bar__item .weui-vtabs-bar__title {
  white-space: normal;
  text-align: center;
}

.evaluate-business-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.evaluate-info-left {
  width: 80%;
  margin: 20rpx 0rpx;
  padding: 0 50rpx;
  border-right: 1rpx solid #f5f5f5;
}

.evaluate-info-right {
  width: 20%;
  padding: 20rpx;
  text-align: center;
}

.business-evaluate {
  display: flex;
  justify-content: space-between;
}

.evaluate-total-score {
  color: #f56427;
  font-size: 40rpx;
  margin-right: 25rpx;
}

.evaluate-total-star {
  color: #4f4f4f;
  font-size: 24rpx;
}

.evaluate-total-num {
  color: #4f4f4f;
  font-size: 35rpx;
}

.view-line {
  height: 20rpx;
  background: #f3f3f3;
}

.evaluate-items {
  padding: 20rpx;
  margin-bottom: 80rpx;
}

.evaluate-item {
  display: flex;
  justify-content: space-between;
  padding-bottom: 30rpx;
  padding-top: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.appraise-reply-items{
  background: #f7f7f7;
  border-radius: 10rpx;
  padding: 10rpx 15rpx;
}

.reply-item{
  font-size: 24rpx;
  color: #4f4f4f;
}

.evaluate-item-detail{
  width: 90%;
}

.evaluate-user-image {
  width: 8%;
  height: 8%;
  border-radius: 50%;
}

.evaluate-itemu-username-time {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.username-detail{
  width: 60%;
  font-size: 28rpx;
}

.images-url{
  width: 100rpx;
  height: 100rpx;
  margin-right: 10rpx;
}

.datetime-detail{
  font-size: 24rpx;
  color: #7d7d7d;
}

.pl-dz-class{
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: end;
}

.appraise-class-pl-dz{
  margin-left: 10rpx;
}

.pl-dz-view{
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #9b9b9b;
}

.pl-dz-display{
  display: flex;
  align-items: center;
}

.input-pinglun{
  width: 100%;
  padding: 20rpx;
}

.userinfo-text{
  font-size: 28rpx;
  text-align: end;
  font-weight: bold;
}

.input-appraise{
  width: 92%;
  border:1rpx solid #8e8e8e;
  padding: 10rpx;
  border-radius: 15rpx;
  margin: 20rpx;
  text-align: start;
  height: 150rpx;
}

.pinglun-bottom{
  position: fixed;
  width: 100%;
  bottom: 0;
  background: white;
  border-top: 1rpx solid #f5f5f5;
}

.reply-button-view{
  text-align: end;
  padding: 0 20rpx 20rpx 20rpx;
}

.extClassShopDetail{
  z-index: 9999;
}

.shop-detail-dialog{
  margin-bottom: 20rpx;
}

.reduced-delivery-price{
  font-size: 28rpx;
  margin-top: 20rpx;
}

.dialog-title{
  font-size: 30rpx;
}

.edit-address-class{
  text-align: center;
  height: 50px;
}

.edit-address-btn{
  margin: 20rpx 0;
}

.icongouwuche1{
  font-size: 40rpx;
}

.iconweibiaoti35-copy{
  font-size: 26rpx;
}
.weui-vtabs-bar__content{
  width: 100%;
}
.mp-vtabs-class-1{
  width: 32%;
  margin-bottom: 70px;
}
.weui-vtabs-bar__item{
  width: 100%;
}
.menu_list{
  position: relative;
}
.menu_scorll_mask{
  position:absolute;
  top: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
}