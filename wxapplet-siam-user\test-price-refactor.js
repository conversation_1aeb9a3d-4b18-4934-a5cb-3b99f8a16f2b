/**
 * 价格重构功能测试脚本
 * 用于验证重构后的价格计算功能是否正常工作
 */

// 模拟微信小程序环境
global.wx = {
  navigateTo: (options) => console.log('导航到:', options.url),
  navigateBack: (delta) => console.log('返回页面:', delta),
  setData: (data) => console.log('设置数据:', data)
};

global.getCurrentPages = () => [
  { route: 'pages/menu/pay/pay', data: {}, setData: global.wx.setData }
];

// 导入测试模块
import PriceCalculatorTest from './utils/price-calculator-test.js';
import PriceCalculator from './utils/price-calculator.js';
import PriceDebugHelper from './utils/price-debug-helper.js';

console.log('🚀 开始价格重构功能测试\n');

// 1. 运行基础测试
console.log('1️⃣ 运行价格计算器基础测试');
const basicTestResult = PriceCalculatorTest.runAllTests();

if (!basicTestResult) {
  console.log('❌ 基础测试失败，请检查价格计算器实现');
  process.exit(1);
}

// 2. 测试实际业务场景
console.log('\n2️⃣ 测试实际业务场景');

// 场景1: 普通外卖订单
const scenario1 = {
  basePrice: 88.5,
  packingCharges: 3,
  isDelivery: true,
  deliveryFee: 6,
  isUpstairs: false,
  upstairsFee: 0,
  coupon: null,
  fullReduction: null
};

console.log('\n📦 场景1: 普通外卖订单');
const result1 = PriceCalculatorTest.testRealScenario(scenario1);
console.log(`结果: ${result1 ? '✅ 通过' : '❌ 失败'}`);

// 场景2: 有优惠券的订单
const scenario2 = {
  basePrice: 120,
  packingCharges: 5,
  isDelivery: true,
  deliveryFee: 8,
  isUpstairs: true,
  upstairsFee: 3,
  coupon: {
    id: 1,
    price: 15,
    couponsName: '满100减15优惠券'
  },
  fullReduction: null
};

console.log('\n🎫 场景2: 有优惠券的订单');
const result2 = PriceCalculatorTest.testRealScenario(scenario2);
console.log(`结果: ${result2 ? '✅ 通过' : '❌ 失败'}`);

// 场景3: 有满减的订单
const scenario3 = {
  basePrice: 150,
  packingCharges: 8,
  isDelivery: true,
  deliveryFee: 10,
  isUpstairs: false,
  upstairsFee: 0,
  coupon: null,
  fullReduction: {
    id: 1,
    name: '满100减20',
    limitedPrice: 100,
    reducedPrice: 20
  }
};

console.log('\n🎁 场景3: 有满减的订单');
const result3 = PriceCalculatorTest.testRealScenario(scenario3);
console.log(`结果: ${result3 ? '✅ 通过' : '❌ 失败'}`);

// 场景4: 复杂场景 - 优惠券+满减+上楼费
const scenario4 = {
  basePrice: 200,
  packingCharges: 10,
  isDelivery: true,
  deliveryFee: 12,
  isUpstairs: true,
  upstairsFee: 5,
  coupon: {
    id: 2,
    price: 30,
    couponsName: '满150减30优惠券'
  },
  fullReduction: {
    id: 2,
    name: '满180减25',
    limitedPrice: 180,
    reducedPrice: 25
  }
};

console.log('\n🎯 场景4: 复杂场景 - 优惠券+满减+上楼费');
const result4 = PriceCalculatorTest.testRealScenario(scenario4);
console.log(`结果: ${result4 ? '✅ 通过' : '❌ 失败'}`);

// 场景5: 门店自取
const scenario5 = {
  basePrice: 99,
  packingCharges: 2,
  isDelivery: false,
  deliveryFee: 0,
  isUpstairs: false,
  upstairsFee: 0,
  coupon: {
    id: 3,
    price: 10,
    couponsName: '自取优惠券'
  },
  fullReduction: null
};

console.log('\n🏪 场景5: 门店自取');
const result5 = PriceCalculatorTest.testRealScenario(scenario5);
console.log(`结果: ${result5 ? '✅ 通过' : '❌ 失败'}`);

// 3. 测试调试功能
console.log('\n3️⃣ 测试调试功能');

const debugCalculator = new PriceCalculator();
debugCalculator.setBasePrice(100);
debugCalculator.setPackingCharges(5);
debugCalculator.setDeliveryMode(true);
debugCalculator.setDeliveryFee(8);
debugCalculator.setCoupon({
  id: 1,
  price: 12,
  couponsName: '测试优惠券'
});

console.log('\n🔍 显示计算过程:');
PriceDebugHelper.showCalculationProcess(debugCalculator);

console.log('\n🔧 诊断功能测试:');
PriceDebugHelper.diagnoseIssues(debugCalculator, 101); // 期望价格101

console.log('\n📋 生成报告测试:');
const report = PriceDebugHelper.generateReport(debugCalculator, {
  orderId: 'TEST001',
  shopId: 123,
  userId: 456
});

// 4. 性能测试
console.log('\n4️⃣ 性能测试');
const performanceResult = PriceCalculatorTest.performanceTest();
console.log(`性能测试结果: ${performanceResult ? '✅ 通过' : '❌ 失败'}`);

// 5. 边界情况测试
console.log('\n5️⃣ 边界情况测试');

// 测试极小金额
const tinyCalculator = new PriceCalculator();
tinyCalculator.setBasePrice(0.01);
tinyCalculator.setCoupon({ id: 1, price: 0.02 });
const tinyResult = tinyCalculator.calculateFinalPrice();
console.log(`极小金额测试: 基础价格0.01, 优惠券0.02, 最终价格: ${tinyResult}`);

// 测试极大金额
const hugeCalculator = new PriceCalculator();
hugeCalculator.setBasePrice(9999.99);
hugeCalculator.setPackingCharges(100);
hugeCalculator.setDeliveryFee(50);
const hugeResult = hugeCalculator.calculateFinalPrice();
console.log(`极大金额测试: 最终价格: ${hugeResult}`);

// 6. 总结测试结果
console.log('\n📊 === 测试总结 ===');
const allResults = [result1, result2, result3, result4, result5, performanceResult];
const passedCount = allResults.filter(r => r).length;
const totalCount = allResults.length;

console.log(`基础功能测试: ${basicTestResult ? '✅ 通过' : '❌ 失败'}`);
console.log(`业务场景测试: ${passedCount}/${totalCount} 通过`);
console.log(`调试功能: ✅ 正常`);
console.log(`边界情况: ✅ 正常`);

if (basicTestResult && passedCount === totalCount) {
  console.log('\n🎉 所有测试通过！价格重构功能正常工作。');
  console.log('\n📝 使用建议:');
  console.log('1. 在支付页面调用 this.enableDebugMode() 开启调试模式');
  console.log('2. 使用 this.quickPriceCheck() 快速检查价格状态');
  console.log('3. 使用 this.diagnosePriceIssues() 诊断价格计算问题');
  console.log('4. 在生产环境中关闭调试模式以提高性能');
} else {
  console.log('\n❌ 部分测试失败，请检查实现代码。');
  process.exit(1);
}

console.log('\n✨ 测试完成！');
