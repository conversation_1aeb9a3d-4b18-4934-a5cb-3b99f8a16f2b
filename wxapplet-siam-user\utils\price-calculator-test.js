import PriceCalculator from './price-calculator';

/**
 * 价格计算器测试工具
 */
class PriceCalculatorTest {
  
  /**
   * 运行所有测试
   */
  static runAllTests() {
    console.log('=== 开始价格计算器测试 ===');
    
    const tests = [
      this.testBasicCalculation,
      this.testDeliveryFeeCalculation,
      this.testCouponCalculation,
      this.testFullReductionCalculation,
      this.testComplexScenario,
      this.testEdgeCases,
      this.testValidation
    ];
    
    let passedTests = 0;
    let totalTests = tests.length;
    
    tests.forEach((test, index) => {
      try {
        console.log(`\n--- 测试 ${index + 1}: ${test.name} ---`);
        const result = test.call(this);
        if (result) {
          console.log('✅ 测试通过');
          passedTests++;
        } else {
          console.log('❌ 测试失败');
        }
      } catch (error) {
        console.log('❌ 测试异常:', error.message);
      }
    });
    
    console.log(`\n=== 测试结果: ${passedTests}/${totalTests} 通过 ===`);
    return passedTests === totalTests;
  }
  
  /**
   * 测试基础计算
   */
  static testBasicCalculation() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(100);
    calculator.setPackingCharges(5);
    
    const result = calculator.calculateFinalPrice();
    const expected = 105;
    
    console.log(`基础价格: 100, 包装费: 5, 最终价格: ${result}, 期望: ${expected}`);
    return Math.abs(result - expected) < 0.01;
  }
  
  /**
   * 测试配送费计算
   */
  static testDeliveryFeeCalculation() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(100);
    calculator.setDeliveryMode(true);
    calculator.setDeliveryFee(10);
    calculator.setUpstairsOption(true, 3);
    
    const result = calculator.calculateFinalPrice();
    const expected = 113; // 100 + 10 + 3
    
    console.log(`基础价格: 100, 配送费: 10, 上楼费: 3, 最终价格: ${result}, 期望: ${expected}`);
    return Math.abs(result - expected) < 0.01;
  }
  
  /**
   * 测试优惠券计算
   */
  static testCouponCalculation() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(100);
    calculator.setPackingCharges(5);
    calculator.setCoupon({
      id: 1,
      price: 20,
      couponsName: '测试优惠券'
    });
    
    const result = calculator.calculateFinalPrice();
    const expected = 85; // 100 + 5 - 20
    
    console.log(`基础价格: 100, 包装费: 5, 优惠券: 20, 最终价格: ${result}, 期望: ${expected}`);
    return Math.abs(result - expected) < 0.01;
  }
  
  /**
   * 测试满减计算
   */
  static testFullReductionCalculation() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(100);
    calculator.setPackingCharges(5);
    calculator.setFullReduction({
      id: 1,
      name: '满100减15',
      limitedPrice: 100,
      reducedPrice: 15
    });
    
    const result = calculator.calculateFinalPrice();
    const expected = 90; // 100 + 5 - 15
    
    console.log(`基础价格: 100, 包装费: 5, 满减: 15, 最终价格: ${result}, 期望: ${expected}`);
    return Math.abs(result - expected) < 0.01;
  }
  
  /**
   * 测试复杂场景
   */
  static testComplexScenario() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(150);
    calculator.setPackingCharges(8);
    calculator.setDeliveryMode(true);
    calculator.setDeliveryFee(12);
    calculator.setUpstairsOption(true, 5);
    calculator.setCoupon({
      id: 1,
      price: 25,
      couponsName: '测试优惠券'
    });
    calculator.setFullReduction({
      id: 1,
      name: '满120减20',
      limitedPrice: 120,
      reducedPrice: 20
    });
    
    const result = calculator.calculateFinalPrice();
    // 计算过程: 150 + 8 - 25 - 20 + 12 + 5 = 130
    const expected = 130;
    
    const details = calculator.getPriceDetails();
    console.log('复杂场景详情:', details);
    console.log(`最终价格: ${result}, 期望: ${expected}`);
    
    return Math.abs(result - expected) < 0.01;
  }
  
  /**
   * 测试边界情况
   */
  static testEdgeCases() {
    // 测试负数价格保护
    const calculator1 = new PriceCalculator();
    calculator1.setBasePrice(10);
    calculator1.setCoupon({ id: 1, price: 20 });
    
    const result1 = calculator1.calculateFinalPrice();
    console.log(`负数保护测试: 基础价格10, 优惠券20, 最终价格: ${result1}`);
    
    // 测试自取模式下的配送费清零
    const calculator2 = new PriceCalculator();
    calculator2.setBasePrice(100);
    calculator2.setDeliveryMode(false);
    calculator2.setDeliveryFee(10);
    calculator2.setUpstairsOption(true, 5);
    
    const result2 = calculator2.calculateFinalPrice();
    const expected2 = 100; // 自取模式，配送费和上楼费都应该为0
    
    console.log(`自取模式测试: 基础价格100, 最终价格: ${result2}, 期望: ${expected2}`);
    
    return result1 >= 0 && Math.abs(result2 - expected2) < 0.01;
  }
  
  /**
   * 测试数据验证
   */
  static testValidation() {
    const calculator = new PriceCalculator();
    calculator.setBasePrice(-10); // 负数基础价格
    
    const validation = calculator.validate();
    console.log('验证结果:', validation);
    
    return !validation.isValid && validation.errors.length > 0;
  }
  
  /**
   * 测试实际业务场景
   */
  static testRealScenario(orderData) {
    console.log('\n=== 实际业务场景测试 ===');
    
    const calculator = new PriceCalculator();
    
    // 设置基础数据
    calculator.setBasePrice(orderData.basePrice || 0);
    calculator.setPackingCharges(orderData.packingCharges || 0);
    calculator.setDeliveryMode(orderData.isDelivery || false);
    
    if (orderData.isDelivery) {
      calculator.setDeliveryFee(orderData.deliveryFee || 0);
      calculator.setUpstairsOption(orderData.isUpstairs || false, orderData.upstairsFee || 0);
    }
    
    if (orderData.coupon) {
      calculator.setCoupon(orderData.coupon);
    }
    
    if (orderData.fullReduction) {
      calculator.setFullReduction(orderData.fullReduction);
    }
    
    const result = calculator.getPriceDetails();
    const validation = calculator.validate();
    
    console.log('业务场景计算结果:', result);
    console.log('数据验证结果:', validation);
    
    return validation.isValid;
  }
  
  /**
   * 性能测试
   */
  static performanceTest() {
    console.log('\n=== 性能测试 ===');
    
    const iterations = 10000;
    const startTime = Date.now();
    
    for (let i = 0; i < iterations; i++) {
      const calculator = new PriceCalculator();
      calculator.setBasePrice(Math.random() * 1000);
      calculator.setPackingCharges(Math.random() * 50);
      calculator.setDeliveryMode(Math.random() > 0.5);
      calculator.setDeliveryFee(Math.random() * 20);
      calculator.setCoupon({
        id: 1,
        price: Math.random() * 100
      });
      calculator.setFullReduction({
        id: 1,
        limitedPrice: 50,
        reducedPrice: Math.random() * 30
      });
      
      calculator.calculateFinalPrice();
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`${iterations} 次计算耗时: ${duration}ms`);
    console.log(`平均每次计算耗时: ${(duration / iterations).toFixed(3)}ms`);
    
    return duration < 1000; // 期望10000次计算在1秒内完成
  }
}

export default PriceCalculatorTest;
